<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.2.2 (Python 3.12.10 on win32)" generated="2025-07-03T10:07:18.733986" rpa="false" schemaversion="5">
<suite id="s1" name="VMS Portal" source="C:\Alternative\Alternative_Physical_Channels_QA\vms\tests\ADMIN_EMAIL_MANAGEMENT\RAC29a_TC_335_Validate_Delete_Link_Email_Management.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-07-03T10:07:24.874929" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-07-03T10:07:24.873914" elapsed="0.001015"/>
</kw>
<arg>'${UPLOAD_TEST_STEPS}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${UPLOAD_TEST_STEPS} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-07-03T10:07:24.873914" elapsed="0.001015"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-07-03T10:07:24.874929" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'Bin_Tables.xml'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-07-03T10:07:24.874929" elapsed="0.000000"/>
</kw>
<arg>'${ROBOT_FILE_PATH}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${ROBOT_FILE_PATH} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-07-03T10:07:24.874929" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-07-03T10:07:24.874929" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-07-03T10:07:24.874929" elapsed="0.000000"/>
</kw>
<arg>'${IS_HEADLESS_BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${IS_HEADLESS_BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-07-03T10:07:24.874929" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-07-03T10:07:24.876409" level="INFO">Environment variable  does not exist.</msg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-07-03T10:07:24.876409" elapsed="0.000000"/>
</kw>
<arg>'${BASE_URL}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-07-03T10:07:24.874929" elapsed="0.001480"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-07-03T10:07:24.876409" level="INFO">Environment variable 'BROWSER' set to value 'edge'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-07-03T10:07:24.876409" elapsed="0.000000"/>
</kw>
<arg>'${BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-07-03T10:07:24.876409" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-07-03T10:07:24.877405" level="INFO">Environment variable 'SUITE_DIRECTORY' set to value 'Alternative_Physical_Channels_QA/vms/data'.</msg>
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-07-03T10:07:24.876409" elapsed="0.000996"/>
</kw>
<arg>'${SUITE_DIRECTORY}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'SUITE_DIRECTORY', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-07-03T10:07:24.876409" elapsed="0.000996"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-07-03T10:07:24.877405" level="INFO">Environment variable called 'MS_USERNAME', does not exist.</msg>
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-07-03T10:07:24.877405" elapsed="0.000000"/>
</kw>
<arg>'${MS_USERNAME}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_USERNAME</arg>
<arg>${MS_USERNAME}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-07-03T10:07:24.877405" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-07-03T10:07:24.877405" level="INFO">Environment variable called 'MS_PASSWORD', does not exist.</msg>
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-07-03T10:07:24.877405" elapsed="0.000000"/>
</kw>
<arg>'${MS_PASSWORD}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_PASSWORD</arg>
<arg>${MS_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-07-03T10:07:24.877405" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${TEST_CASE_ID}' != ''">
<kw name="Get Test Case ID" owner="SetEnvironmentVariales">
<var>${TEST_CASE_ID}</var>
<arg>${TEST_CASE_ID}</arg>
<status status="NOT RUN" start="2025-07-03T10:07:24.878404" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Test Case ID: ${TEST_CASE_ID}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-07-03T10:07:24.878404" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-07-03T10:07:24.878404" elapsed="0.001002"/>
</branch>
<status status="PASS" start="2025-07-03T10:07:24.878404" elapsed="0.001002"/>
</if>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-07-03T10:07:24.879406" level="INFO">Environment variable called 'TEST_CASE_ID', does not exist.</msg>
<arg>Environment variable called 'TEST_CASE_ID', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-07-03T10:07:24.879406" elapsed="0.000000"/>
</kw>
<arg>'${TEST_CASE_ID}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>TEST_CASE_ID</arg>
<arg>${TEST_CASE_ID}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'TEST_CASE_ID', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-07-03T10:07:24.879406" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-07-03T10:07:24.872925" elapsed="0.007468"/>
</kw>
<test id="s1-t1" name="Validate Delete Link- Email Management: Delete a random existing vendor email." line="35">
<kw name="Validate Delete Link-Email Management">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2025-07-03T10:07:24.881388" level="INFO">Set test documentation to:
Validate the delete functionality for vendor emails in Email Management.</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2025-07-03T10:07:24.881388" elapsed="0.001002"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-07-03T10:07:24.883390" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-07-03T10:07:24.883390" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-07-03T10:07:24.883390" elapsed="0.001509"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-07-03T10:07:24.884899" elapsed="0.001008"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="VMS Windows system Login" owner="Login">
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-07-03T10:07:24.886910" level="FAIL">Environment variable 'BASE_URL' does not exist.</msg>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="FAIL" start="2025-07-03T10:07:24.886910" elapsed="0.000000">Environment variable 'BASE_URL' does not exist.</status>
</kw>
<msg time="2025-07-03T10:07:24.886910" level="INFO">${url_exists_on_env_var} = False</msg>
<var>${url_exists_on_env_var}</var>
<arg>Get Environment Variable</arg>
<arg>BASE_URL</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-07-03T10:07:24.886910" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="${url_exists_on_env_var}">
<kw name="Get Environment Variable" owner="OperatingSystem">
<var>${BASE_URL}</var>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="NOT RUN" start="2025-07-03T10:07:24.887912" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-07-03T10:07:24.886910" elapsed="0.001002"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-07-03T10:07:24.887912" level="INFO">${BASE_URL} = VMS_UAT</msg>
<var>${BASE_URL}</var>
<arg>${URL}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-07-03T10:07:24.887912" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-07-03T10:07:24.887912" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-07-03T10:07:24.886910" elapsed="0.001002"/>
</if>
<kw name="Read Config Property" owner="CommonUtils">
<msg time="2025-07-03T10:07:24.940565" level="INFO">Property value fetched is:  https://vms.uat.absa.africa/</msg>
<msg time="2025-07-03T10:07:24.940565" level="INFO">${base_url} = https://vms.uat.absa.africa/</msg>
<var>${base_url}</var>
<arg>${BASE_URL}</arg>
<status status="PASS" start="2025-07-03T10:07:24.887912" elapsed="0.052653"/>
</kw>
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-07-03T10:07:24.974824" level="INFO">${PROCESS_NAME_LowerCase} = edge</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-07-03T10:07:24.941562" elapsed="0.033262"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-07-03T10:07:24.974824" level="INFO">${PROCESS_NAME_LowerCase} = msedge</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-07-03T10:07:24.974824" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-07-03T10:07:24.974824" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-07-03T10:07:24.974824" elapsed="0.000000"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-07-03T10:07:24.975835" level="INFO">${handle} = msedge.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-07-03T10:07:24.975835" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-07-03T10:07:24.975835" elapsed="0.000000"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2025-07-03T10:07:25.190886" level="INFO">Running command 'taskkill /F /IM msedge.exe 2&gt;&amp;1'.</msg>
<msg time="2025-07-03T10:07:26.136016" level="INFO">${rc_code} = 128</msg>
<msg time="2025-07-03T10:07:26.136016" level="INFO">${output} = ERROR: The process "msedge.exe" not found.</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2025-07-03T10:07:24.975835" elapsed="1.160181"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-07-03T10:07:26.136016" level="WARN">There was error during termination of process</msg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-07-03T10:07:26.136016" elapsed="0.001002"/>
</kw>
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-07-03T10:07:26.136016" elapsed="0.001002"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2025-07-03T10:07:24.941562" elapsed="1.195456"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-07-03T10:07:26.137018" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2025-07-03T10:07:26.137018" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-07-03T10:07:26.138029" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-07-03T10:07:26.138029" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-07-03T10:07:26.138029" level="INFO">${browser_name} = EDGE</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-07-03T10:07:26.138029" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-07-03T10:07:26.139040" level="INFO">${user_home} = C:\Users\<USER>\Users\AB038N8</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-07-03T10:07:26.139040" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-07-03T10:07:26.140036" level="INFO">${edgedriver_path} = C:/bin/msedgedriver.exe</msg>
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-07-03T10:07:26.140036" elapsed="0.000000"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<msg time="2025-07-03T10:07:26.140036" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x00000195A0EC0EF0&gt;</msg>
<var>${edge_options}</var>
<status status="PASS" start="2025-07-03T10:07:26.140036" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2025-07-03T10:07:26.141020" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-07-03T10:07:26.141020" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-07-03T10:07:26.141020" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-07-03T10:07:26.141020" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x0000019587436660&gt;</msg>
<var>${edge_options}</var>
<arg>sys.modules['selenium.webdriver'].EdgeOptions()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-07-03T10:07:26.141020" elapsed="0.000997"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-07-03T10:07:26.142017" level="INFO">${root} = C:\Users\<USER>\</msg>
<var>${root}</var>
<arg>${user_home}\\</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-07-03T10:07:26.142017" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-07-03T10:07:26.142017" level="INFO">${path} = Downloads</msg>
<var>${path}</var>
<arg>Downloads</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-07-03T10:07:26.142017" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-07-03T10:07:26.143020" level="INFO">${downl_path} = C:\Users\<USER>\Downloads</msg>
<var>${downl_path}</var>
<arg>${root}${path}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-07-03T10:07:26.142017" elapsed="0.001003"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2025-07-03T10:07:26.144019" level="INFO">${prefs} = {'download.default_directory': 'C:\\Users\\<USER>\\Downloads', 'download.directory_upgrade': 'True', 'savefile.default_directory': 'C:\\Users\\<USER>\\Downloads', 'download.prompt_for_download': 'Fa...</msg>
<var>${prefs}</var>
<arg>download.default_directory=${downl_path}</arg>
<arg>download.directory_upgrade=True</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>savefile.default_directory=${downl_path}</arg>
<arg>download.prompt_for_download=False</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2025-07-03T10:07:26.143020" elapsed="0.000999"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${edge_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2025-07-03T10:07:26.144019" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<msg time="2025-07-03T10:07:26.145018" level="WARN">exexcutable_path is being deprecated. Please use service to configure the driver's executable_path as per documentation.</msg>
<msg time="2025-07-03T10:07:26.145536" level="INFO">Opening browser 'edge' to base url 'about:blank'.</msg>
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="PASS" start="2025-07-03T10:07:26.145018" elapsed="4.642837"/>
</kw>
<status status="PASS" start="2025-07-03T10:07:26.141020" elapsed="4.646835"/>
</branch>
<status status="PASS" start="2025-07-03T10:07:26.141020" elapsed="4.646835"/>
</if>
<status status="PASS" start="2025-07-03T10:07:26.140036" elapsed="4.647819"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-07-03T10:07:30.788854" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-07-03T10:07:30.789851" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-07-03T10:07:30.790857" elapsed="0.000994"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-07-03T10:07:30.791851" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-07-03T10:07:30.790857" elapsed="0.000994"/>
</branch>
<status status="NOT RUN" start="2025-07-03T10:07:30.790857" elapsed="0.001994"/>
</if>
<kw name="Set Variable" owner="BuiltIn">
<var>${root}</var>
<arg>${user_home}\\</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-07-03T10:07:30.792851" elapsed="0.000998"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${path}</var>
<arg>Downloads</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-07-03T10:07:30.793849" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${downl_path}</var>
<arg>${root}${path}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-07-03T10:07:30.794853" elapsed="0.000512"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>download.directory_upgrade=True</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>savefile.default_directory=${downl_path}</arg>
<arg>download.prompt_for_download=False</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="NOT RUN" start="2025-07-03T10:07:30.795365" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-07-03T10:07:30.796380" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-07-03T10:07:30.797390" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-07-03T10:07:30.798392" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-07-03T10:07:30.799392" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--incognito</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-07-03T10:07:30.800394" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-07-03T10:07:30.801394" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-07-03T10:07:30.802392" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-07-03T10:07:30.803392" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${chrome_options}</arg>
<arg>executable_path=${chromedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-07-03T10:07:30.804392" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-07-03T10:07:30.787855" elapsed="0.016537"/>
</branch>
<status status="PASS" start="2025-07-03T10:07:26.140036" elapsed="4.664869"/>
</if>
<status status="PASS" start="2025-07-03T10:07:24.941562" elapsed="5.863343"/>
</kw>
<kw name="Load" owner="Login">
<kw name="Maximize Browser Window" owner="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" start="2025-07-03T10:07:30.806930" elapsed="0.113067"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Go To" owner="SeleniumLibrary">
<msg time="2025-07-03T10:07:30.926542" level="INFO">Opening url 'https://vms.uat.absa.africa/'</msg>
<arg>${URL}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<status status="PASS" start="2025-07-03T10:07:30.925547" elapsed="4.240152"/>
</kw>
<arg>'${URL}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${URL}</arg>
<arg>ELSE IF</arg>
<arg>'$${URL}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-07-03T10:07:30.919997" elapsed="4.245702"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-07-03T10:07:45.168378" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-07-03T10:07:35.166715" elapsed="10.001663"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-07-03T10:07:45.189296" level="INFO">${element_count_1} = 0</msg>
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-07-03T10:07:45.169399" elapsed="0.019897"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Navigation.Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-07-03T10:07:45.189296" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-07-03T10:07:55.189837" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-07-03T10:07:45.189296" elapsed="10.000541"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-07-03T10:07:55.203076" level="INFO">${element_count_2} = 0</msg>
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-07-03T10:07:55.189837" elapsed="0.013239"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Navigation.Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-07-03T10:07:55.203076" elapsed="0.001000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-07-03T10:08:00.204468" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-07-03T10:07:55.204076" elapsed="5.000392"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:00.219363" level="INFO">${element_count_3} = 0</msg>
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-07-03T10:08:00.204468" elapsed="0.014895"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-07-03T10:08:00.220361" elapsed="0.000000"/>
</kw>
<arg>${base_url}</arg>
<status status="PASS" start="2025-07-03T10:07:30.805920" elapsed="29.414441"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:00.368399" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-07-03T10:08:00.220361" elapsed="0.148038"/>
</kw>
<msg time="2025-07-03T10:08:00.368399" level="INFO">${User_Name_Element_Visible} = True</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-07-03T10:08:00.220361" elapsed="0.148038"/>
</kw>
<while condition="${User_Name_Element_Visible}">
<iter>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:00.369392" level="INFO">Typing text 'AB038N8' into text field 'name=txtUsername'.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-07-03T10:08:00.369392" elapsed="0.103504"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-07-03T10:08:00.472896" elapsed="0.025533"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:00.499427" level="INFO">Typing text '67355870@SitholeBrother' into text field 'name=txtPassword'.</msg>
<arg>${VMS_PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-07-03T10:08:00.499427" elapsed="0.171869"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:00.672295" level="INFO">Clicking button 'name=btnLogon'.</msg>
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-07-03T10:08:00.671296" elapsed="3.595845"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-07-03T10:08:06.268361" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-07-03T10:08:04.267141" elapsed="2.001220"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:06.455696" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-5.png"&gt;&lt;img src="selenium-screenshot-5.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2025-07-03T10:08:06.455696" level="FAIL">Element with locator 'name=txtUsername' not found.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="FAIL" start="2025-07-03T10:08:06.269670" elapsed="0.382817">Element with locator 'name=txtUsername' not found.</status>
</kw>
<msg time="2025-07-03T10:08:06.654225" level="INFO">${User_Name_Element_Visible} = False</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-07-03T10:08:06.268361" elapsed="0.385864"/>
</kw>
<status status="PASS" start="2025-07-03T10:08:00.368399" elapsed="6.285826"/>
</iter>
<status status="PASS" start="2025-07-03T10:08:00.368399" elapsed="6.286630"/>
</while>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------------ User successfully accessed VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-07-03T10:08:06.656048" elapsed="0.000990"/>
</kw>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:06.847617" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-6.png"&gt;&lt;img src="selenium-screenshot-6.png" width="800px"&gt;&lt;/a&gt;</msg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="PASS" start="2025-07-03T10:08:06.658039" elapsed="0.190572"/>
</kw>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<status status="PASS" start="2025-07-03T10:07:24.885907" elapsed="41.962704"/>
</kw>
<arg>'${system}' == 'Windows'</arg>
<arg>VMS Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-07-03T10:07:24.885907" elapsed="41.962704"/>
</kw>
<arg>${TEST_ENVIRONMENT}</arg>
<status status="PASS" start="2025-07-03T10:07:24.882390" elapsed="41.966221"/>
</kw>
<kw name="When The user navigates to Admin - Email Management" owner="EmailManagement">
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:06.895166" level="INFO">Current page contains text 'Dashboard'.</msg>
<arg>Dashboard</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-07-03T10:08:06.852163" elapsed="0.043003"/>
</kw>
<msg time="2025-07-03T10:08:06.896225" level="INFO">${on_dashboard} = True</msg>
<var>${on_dashboard}</var>
<arg>SeleniumLibrary.Page Should Contain</arg>
<arg>Dashboard</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-07-03T10:08:06.851173" elapsed="0.045052"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>not ${on_dashboard}</arg>
<arg>Navigate To Dashboard</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-07-03T10:08:06.896225" elapsed="0.001002"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${ADMIN_LINK}</arg>
<arg>10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-07-03T10:08:06.898231" elapsed="0.059921"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:07.000619" level="INFO">Current page contains text 'Dashboard'.</msg>
<arg>Dashboard</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-07-03T10:08:06.959149" elapsed="0.041470"/>
</kw>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:07.170345" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="VMS_Landing_Page.png"&gt;&lt;img src="VMS_Landing_Page.png" width="800px"&gt;&lt;/a&gt;</msg>
<arg>VMS_Landing_Page.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="PASS" start="2025-07-03T10:08:07.001659" elapsed="0.168686"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:07.172394" level="INFO">Clicking element 'xpath=//*[text()[normalize-space(.)='Admin']]'.</msg>
<arg>${ADMIN_LINK}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-07-03T10:08:07.171613" elapsed="0.212208"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:07.385786" level="INFO">Clicking element 'xpath=//a[contains(@class,'nav-link')][text()[normalize-space(.)='Email Management']]'.</msg>
<arg>${EMAIL_MANAGEMENT_LINK}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-07-03T10:08:07.383821" elapsed="0.429194"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:07.856726" level="INFO">Current page contains text 'Email Management'.</msg>
<arg>Email Management</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-07-03T10:08:07.813015" elapsed="0.044715"/>
</kw>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:08.006450" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="Email_Management_Landing_Page.png"&gt;&lt;img src="Email_Management_Landing_Page.png" width="800px"&gt;&lt;/a&gt;</msg>
<arg>Email_Management_Landing_Page.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="PASS" start="2025-07-03T10:08:07.857730" elapsed="0.149720"/>
</kw>
<status status="PASS" start="2025-07-03T10:08:06.850245" elapsed="1.157205"/>
</kw>
<kw name="And Delete Random Email From Database" owner="EmailManagement">
<kw name="Get Random VMS Email" owner="DatabaseConnector">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-07-03T10:08:08.008467" level="INFO">${my_query} = SELECT TOP 1 [Link], [Vendor], [Email] FROM [VMS_UAT].[core].[email] ORDER BY NEWID()</msg>
<var>${my_query}</var>
<arg>${SQL_GET_RANDOM_VMS_EMAIL}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-07-03T10:08:08.007450" elapsed="0.001017"/>
</kw>
<kw name="Log Many" owner="BuiltIn">
<msg time="2025-07-03T10:08:08.008467" level="INFO">SELECT TOP 1 [Link], [Vendor], [Email] FROM [VMS_UAT].[core].[email] ORDER BY NEWID()</msg>
<arg>${my_query}</arg>
<doc>Logs the given messages as separate entries using the INFO level.</doc>
<status status="PASS" start="2025-07-03T10:08:08.008467" elapsed="0.000000"/>
</kw>
<kw name="Execute VMS Query" owner="DatabaseConnector">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-07-03T10:08:08.008467" level="INFO">${DB_TYPE} = MSSQL</msg>
<var>${DB_TYPE}</var>
<arg>MSSQL</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-07-03T10:08:08.008467" elapsed="0.000000"/>
</kw>
<kw name="Read Config Property" owner="CommonUtils">
<msg time="2025-07-03T10:08:08.012466" level="INFO">Property value fetched is:  XZAPBCC1SQL1004</msg>
<msg time="2025-07-03T10:08:08.012466" level="INFO">${DB_HOST} = XZAPBCC1SQL1004</msg>
<var>${DB_HOST}</var>
<arg>MS_DB_HOST</arg>
<status status="PASS" start="2025-07-03T10:08:08.008467" elapsed="0.003999"/>
</kw>
<kw name="Read Config Property" owner="CommonUtils">
<msg time="2025-07-03T10:08:08.019006" level="INFO">Property value fetched is:  VMS_UAT</msg>
<msg time="2025-07-03T10:08:08.020006" level="INFO">${DB_NAME} = VMS_UAT</msg>
<var>${DB_NAME}</var>
<arg>MS_DB_SCHEMA</arg>
<status status="PASS" start="2025-07-03T10:08:08.012466" elapsed="0.007540"/>
</kw>
<kw name="Read Config Property" owner="CommonUtils">
<msg time="2025-07-03T10:08:08.025542" level="INFO">Property value fetched is:  apl</msg>
<msg time="2025-07-03T10:08:08.025542" level="INFO">${DB_USER} = apl</msg>
<var>${DB_USER}</var>
<arg>MS_DB_User</arg>
<status status="PASS" start="2025-07-03T10:08:08.020006" elapsed="0.005536"/>
</kw>
<kw name="Read Config Property" owner="CommonUtils">
<msg time="2025-07-03T10:08:08.030561" level="INFO">Property value fetched is:  Pa$$w0rd</msg>
<msg time="2025-07-03T10:08:08.030561" level="INFO">${DB_PASSWORD} = Pa$$w0rd</msg>
<var>${DB_PASSWORD}</var>
<arg>MS_DB_PWD</arg>
<status status="PASS" start="2025-07-03T10:08:08.025542" elapsed="0.005019"/>
</kw>
<kw name="Execute Select Query" owner="DatabaseConnector">
<kw name="Execute Select Statement" owner="Database_Library">
<msg time="2025-07-03T10:08:08.189673" level="INFO">MSSQL XZAPBCC1SQL1004 VMS_UAT apl Pa$$w0rd
Connection established, executing query.</msg>
<msg time="2025-07-03T10:08:08.189673" level="INFO">${results} = [{'Link': 603, 'Vendor': 'BMS                                ', 'Email': '<EMAIL>'}]</msg>
<var>${results}</var>
<arg>${db_type}</arg>
<arg>${host}</arg>
<arg>${database}</arg>
<arg>${username}</arg>
<arg>${password}</arg>
<arg>${query}</arg>
<doc>Executes a SELECT query on either MySQL or MSSQL and returns the results as a dictionary.
Raises an error if the query fails.</doc>
<status status="PASS" start="2025-07-03T10:08:08.030561" elapsed="0.159112"/>
</kw>
<kw name="Return From Keyword" owner="BuiltIn">
<msg time="2025-07-03T10:08:08.189673" level="INFO">Returning from the enclosing user keyword.</msg>
<arg>${results}</arg>
<doc>Returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-07-03T10:08:08.189673" elapsed="0.000996"/>
</kw>
<msg time="2025-07-03T10:08:08.190669" level="INFO">${results} = [{'Link': 603, 'Vendor': 'BMS                                ', 'Email': '<EMAIL>'}]</msg>
<var>${results}</var>
<arg>${DB_TYPE}</arg>
<arg>${DB_HOST}</arg>
<arg>${DB_NAME}</arg>
<arg>${DB_USER}</arg>
<arg>${DB_PASSWORD}</arg>
<arg>${query}</arg>
<doc>Execute a database query against the VMS database and return the results
This keyword handles the connection to the VMS database,
executes the query, and returns the results.</doc>
<status status="PASS" start="2025-07-03T10:08:08.030561" elapsed="0.160108"/>
</kw>
<return>
<value>${results}</value>
<status status="PASS" start="2025-07-03T10:08:08.190669" elapsed="0.000000"/>
</return>
<msg time="2025-07-03T10:08:08.190669" level="INFO">${results} = [{'Link': 603, 'Vendor': 'BMS                                ', 'Email': '<EMAIL>'}]</msg>
<var>${results}</var>
<arg>${my_query}</arg>
<doc>Execute a query against the VMS database using centralized connection parameters
This keyword handles all database connection details internally.</doc>
<status status="PASS" start="2025-07-03T10:08:08.008467" elapsed="0.182202"/>
</kw>
<kw name="Log Many" owner="BuiltIn">
<msg time="2025-07-03T10:08:08.190669" level="INFO">[{'Link': 603, 'Vendor': 'BMS                                ', 'Email': '<EMAIL>'}]</msg>
<arg>${results}</arg>
<doc>Logs the given messages as separate entries using the INFO level.</doc>
<status status="PASS" start="2025-07-03T10:08:08.190669" elapsed="0.000000"/>
</kw>
<kw name="Should Not Be Empty" owner="BuiltIn">
<msg time="2025-07-03T10:08:08.190669" level="INFO">Length is 1.</msg>
<arg>${results}</arg>
<arg>msg=No emails found in the database</arg>
<doc>Verifies that the given item is not empty.</doc>
<status status="PASS" start="2025-07-03T10:08:08.190669" elapsed="0.000000"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2025-07-03T10:08:08.191667" level="INFO">${random_email_record} = {'Link': 603, 'Vendor': 'BMS                                ', 'Email': '<EMAIL>'}</msg>
<var>${random_email_record}</var>
<arg>${results}</arg>
<arg>0</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-07-03T10:08:08.190669" elapsed="0.000998"/>
</kw>
<return>
<value>${random_email_record}</value>
<status status="PASS" start="2025-07-03T10:08:08.191667" elapsed="0.000000"/>
</return>
<msg time="2025-07-03T10:08:08.191667" level="INFO">${random_email_record} = {'Link': 603, 'Vendor': 'BMS                                ', 'Email': '<EMAIL>'}</msg>
<var>${random_email_record}</var>
<doc>Get a random email from the VMS database for testing purposes</doc>
<status status="PASS" start="2025-07-03T10:08:08.007450" elapsed="0.184217"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<msg time="2025-07-03T10:08:08.191667" level="INFO">${original_link_id_raw} = 603</msg>
<var>${original_link_id_raw}</var>
<arg>${random_email_record}</arg>
<arg>Link</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="PASS" start="2025-07-03T10:08:08.191667" elapsed="0.000000"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<msg time="2025-07-03T10:08:08.191667" level="INFO">${original_vendor} = BMS                                </msg>
<var>${original_vendor}</var>
<arg>${random_email_record}</arg>
<arg>Vendor</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="PASS" start="2025-07-03T10:08:08.191667" elapsed="0.000000"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<msg time="2025-07-03T10:08:08.191667" level="INFO">${original_email} = <EMAIL></msg>
<var>${original_email}</var>
<arg>${random_email_record}</arg>
<arg>Email</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="PASS" start="2025-07-03T10:08:08.191667" elapsed="0.000000"/>
</kw>
<kw name="Convert To String" owner="BuiltIn">
<msg time="2025-07-03T10:08:08.191667" level="INFO">${original_link_id} = 603</msg>
<var>${original_link_id}</var>
<arg>${original_link_id_raw}</arg>
<doc>Converts the given item to a Unicode string.</doc>
<status status="PASS" start="2025-07-03T10:08:08.191667" elapsed="0.001001"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-07-03T10:08:08.192668" level="INFO">Original Email Details to Delete - Link ID: 603, Vendor: BMS                                , Email: <EMAIL></msg>
<arg>Original Email Details to Delete - Link ID: ${original_link_id}, Vendor: ${original_vendor}, Email: ${original_email}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-07-03T10:08:08.192668" elapsed="0.000000"/>
</kw>
<kw name="Searches for existing user" owner="EmailManagement">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:08.210005" level="INFO">Current page contains text 'Email Management'.</msg>
<arg>Email Management</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-07-03T10:08:08.192668" elapsed="0.017337"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-07-03T10:08:09.210198" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-07-03T10:08:08.210005" elapsed="1.000193"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:09.211697" level="INFO">Typing text '<EMAIL>' into text field 'xpath=//div[@id='VendorEmail']/descendant::input[@id='searchField']'.</msg>
<arg>${VENDOR_SEARCH_INPUT}</arg>
<arg>${SEARCH_DATA}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-07-03T10:08:09.210198" elapsed="0.318085"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-07-03T10:08:11.528566" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-07-03T10:08:09.528283" elapsed="2.000283"/>
</kw>
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:11.557270" level="INFO">Element 'xpath=//table[contains(@class,'table')][contains(@class,'gs-table')]' is displayed.</msg>
<arg>${VMS_USERS_TABLE}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-07-03T10:08:11.528566" elapsed="0.028704"/>
</kw>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-07-03T10:08:11.557270" level="INFO">${table_tbody} = xpath=//table[contains(@class,'table')][contains(@class,'gs-table')] /tbody[contains(@class,'gs-table-body')]</msg>
<var>${table_tbody}</var>
<arg>${VMS_USERS_TABLE}</arg>
<arg>/tbody[contains(@class,'gs-table-body')]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-07-03T10:08:11.557270" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:11.579483" level="INFO">Element 'xpath=//table[contains(@class,'table')][contains(@class,'gs-table')] /tbody[contains(@class,'gs-table-body')]' is displayed.</msg>
<arg>${table_tbody}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-07-03T10:08:11.557270" elapsed="0.022787"/>
</kw>
<msg time="2025-07-03T10:08:11.580057" level="INFO">${IsElementVisible} = True</msg>
<var>${IsElementVisible}</var>
<arg>Element Should Be Visible</arg>
<arg>${table_tbody}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-07-03T10:08:11.557270" elapsed="0.022787"/>
</kw>
<if>
<branch type="IF" condition="${IsElementVisible} == $True">
<kw name="Log" owner="BuiltIn">
<msg time="2025-07-03T10:08:11.580626" level="INFO">The Vendor: '<EMAIL>' was returned on the search results, which means it exists on VMS.</msg>
<arg>The Vendor: '${SEARCH_DATA}' was returned on the search results, which means it exists on VMS.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-07-03T10:08:11.580626" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-07-03T10:08:11.580057" elapsed="0.000569"/>
</branch>
<branch type="ELSE">
<kw name="Fail" owner="BuiltIn">
<arg>The Vendor: '${SEARCH_DATA}' was not returned on the search results, which means it does not exist on VMS</arg>
<doc>Fails the test with the given message and optionally alters its tags.</doc>
<status status="NOT RUN" start="2025-07-03T10:08:11.580626" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-07-03T10:08:11.580626" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-07-03T10:08:11.580057" elapsed="0.000569"/>
</if>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:11.708970" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="User_Search_thabobenjamin.setuke%40absa.africa.png"&gt;&lt;img src="User_Search_thabobenjamin.setuke%40absa.africa.png" width="800px"&gt;&lt;/a&gt;</msg>
<arg>User_Search_${SEARCH_DATA}.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="PASS" start="2025-07-03T10:08:11.580626" elapsed="0.129341"/>
</kw>
<arg>${original_email}</arg>
<status status="PASS" start="2025-07-03T10:08:08.192668" elapsed="3.517299"/>
</kw>
<kw name="Get Current Link ID From UI" owner="EmailManagement">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:11.754131" level="INFO">Current page contains text 'Email Management'.</msg>
<arg>Email Management</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-07-03T10:08:11.712966" elapsed="0.041165"/>
</kw>
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:11.802288" level="INFO">Element 'xpath=//table[contains(@class,'table')][contains(@class,'gs-table')]' is displayed.</msg>
<arg>${VMS_USERS_TABLE}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-07-03T10:08:11.755140" elapsed="0.047148"/>
</kw>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-07-03T10:08:11.804826" level="INFO">${table_tbody} = xpath=//table[contains(@class,'table')][contains(@class,'gs-table')] /tbody[contains(@class,'gs-table-body')]</msg>
<var>${table_tbody}</var>
<arg>${VMS_USERS_TABLE}</arg>
<arg>/tbody[contains(@class,'gs-table-body')]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-07-03T10:08:11.803945" elapsed="0.000881"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:11.825086" level="INFO">${row_count} = 5</msg>
<var>${row_count}</var>
<arg>${table_tbody}/tr</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-07-03T10:08:11.804826" elapsed="0.020260"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-07-03T10:08:11.826092" level="INFO">Found 5 rows in search results</msg>
<arg>Found ${row_count} rows in search results</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-07-03T10:08:11.825086" elapsed="0.001006"/>
</kw>
<for flavor="IN RANGE">
<iter>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-07-03T10:08:11.831105" level="INFO">${table_rows_element} = xpath=//table[contains(@class,'table')][contains(@class,'gs-table')] /tbody[contains(@class,'gs-table-body')]/tr[1]</msg>
<var>${table_rows_element}</var>
<arg>${table_tbody}/tr[${i}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-07-03T10:08:11.830087" elapsed="0.001018"/>
</kw>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-07-03T10:08:11.831105" level="INFO">${id_element} = xpath=//table[contains(@class,'table')][contains(@class,'gs-table')] /tbody[contains(@class,'gs-table-body')]/tr[1]/td[1]</msg>
<var>${id_element}</var>
<arg>${table_rows_element}/td[1]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-07-03T10:08:11.831105" elapsed="0.000000"/>
</kw>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-07-03T10:08:11.832178" level="INFO">${vendor_name_element} = xpath=//table[contains(@class,'table')][contains(@class,'gs-table')] /tbody[contains(@class,'gs-table-body')]/tr[1]/td[2]</msg>
<var>${vendor_name_element}</var>
<arg>${table_rows_element}/td[2]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-07-03T10:08:11.832083" elapsed="0.000095"/>
</kw>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-07-03T10:08:11.832178" level="INFO">${email_element} = xpath=//table[contains(@class,'table')][contains(@class,'gs-table')] /tbody[contains(@class,'gs-table-body')]/tr[1]/td[3]</msg>
<var>${email_element}</var>
<arg>${table_rows_element}/td[3]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-07-03T10:08:11.832178" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:11.854075" level="INFO">Element 'xpath=//table[contains(@class,'table')][contains(@class,'gs-table')] /tbody[contains(@class,'gs-table-body')]/tr[1]/td[3]' is displayed.</msg>
<arg>${email_element}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-07-03T10:08:11.832178" elapsed="0.021897"/>
</kw>
<msg time="2025-07-03T10:08:11.854075" level="INFO">${row_exists} = True</msg>
<var>${row_exists}</var>
<arg>SeleniumLibrary.Element Should Be Visible</arg>
<arg>${email_element}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-07-03T10:08:11.832178" elapsed="0.021897"/>
</kw>
<if>
<branch type="IF" condition="${row_exists}">
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:11.878911" level="INFO">${id_text} = 580</msg>
<var>${id_text}</var>
<arg>${id_element}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-07-03T10:08:11.854075" elapsed="0.025795"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:11.905762" level="INFO">${vendor_name_text} = Thabo Test</msg>
<var>${vendor_name_text}</var>
<arg>${vendor_name_element}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-07-03T10:08:11.879870" elapsed="0.025892"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:11.924020" level="INFO">${email_text} = <EMAIL></msg>
<var>${email_text}</var>
<arg>${email_element}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-07-03T10:08:11.905762" elapsed="0.018258"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-07-03T10:08:11.924020" level="INFO">Row 1 - ID: "580", Vendor: "Thabo Test", Email: "<EMAIL>"</msg>
<arg>Row ${i} - ID: "${id_text}", Vendor: "${vendor_name_text}", Email: "${email_text}"</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-07-03T10:08:11.924020" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<arg>${email_text}</arg>
<arg>${email}</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" start="2025-07-03T10:08:11.924905" elapsed="0.000000"/>
</kw>
<msg time="2025-07-03T10:08:11.924905" level="INFO">${email_match} = True</msg>
<var>${email_match}</var>
<arg>Should Be Equal As Strings</arg>
<arg>${email_text}</arg>
<arg>${email}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-07-03T10:08:11.924905" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="${email_match}">
<kw name="Log" owner="BuiltIn">
<msg time="2025-07-03T10:08:11.924905" level="INFO">Found matching email in row 1</msg>
<arg>Found matching email in row ${i}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-07-03T10:08:11.924905" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-07-03T10:08:11.925906" level="INFO">Current Link ID extracted from UI: 580</msg>
<arg>Current Link ID extracted from UI: ${id_text.strip()}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-07-03T10:08:11.924905" elapsed="0.001001"/>
</kw>
<return>
<value>${id_text.strip()}</value>
<status status="PASS" start="2025-07-03T10:08:11.925906" elapsed="0.000000"/>
</return>
<status status="PASS" start="2025-07-03T10:08:11.924905" elapsed="0.001001"/>
</branch>
<status status="PASS" start="2025-07-03T10:08:11.924905" elapsed="0.001001"/>
</if>
<status status="PASS" start="2025-07-03T10:08:11.854075" elapsed="0.071831"/>
</branch>
<status status="PASS" start="2025-07-03T10:08:11.854075" elapsed="0.071831"/>
</if>
<var name="${i}">1</var>
<status status="PASS" start="2025-07-03T10:08:11.830087" elapsed="0.095819"/>
</iter>
<var>${i}</var>
<value>1</value>
<value>${row_count + 1}</value>
<status status="PASS" start="2025-07-03T10:08:11.826092" elapsed="0.099814"/>
</for>
<kw name="Fail" owner="BuiltIn">
<arg>Email ${email} not found in search results</arg>
<doc>Fails the test with the given message and optionally alters its tags.</doc>
<status status="NOT RUN" start="2025-07-03T10:08:11.926904" elapsed="0.000000"/>
</kw>
<msg time="2025-07-03T10:08:11.926904" level="INFO">${current_link_id} = 580</msg>
<var>${current_link_id}</var>
<arg>${original_email}</arg>
<arg>${original_vendor}</arg>
<doc>Extract the current link ID from the UI table for the specified email</doc>
<status status="PASS" start="2025-07-03T10:08:11.710968" elapsed="0.215936"/>
</kw>
<kw name="The user deletes vendor email" owner="EmailManagement">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:11.940524" level="INFO">Current page contains text 'Email Management'.</msg>
<arg>Email Management</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-07-03T10:08:11.927901" elapsed="0.012623"/>
</kw>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-07-03T10:08:11.941524" level="INFO">${table_tbody} = xpath=//table[contains(@class,'table')][contains(@class,'gs-table')] /tbody[contains(@class,'gs-table-body')]</msg>
<var>${table_tbody}</var>
<arg>${VMS_USERS_TABLE}</arg>
<arg>/tbody[contains(@class,'gs-table-body')]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-07-03T10:08:11.941524" elapsed="0.000000"/>
</kw>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-07-03T10:08:11.941524" level="INFO">${table_rows_element} = xpath=//table[contains(@class,'table')][contains(@class,'gs-table')] /tbody[contains(@class,'gs-table-body')]/tr[1]</msg>
<var>${table_rows_element}</var>
<arg>${table_tbody}/tr[1]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-07-03T10:08:11.941524" elapsed="0.000000"/>
</kw>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-07-03T10:08:11.941524" level="INFO">${delete_button} = xpath=//table[contains(@class,'table')][contains(@class,'gs-table')] /tbody[contains(@class,'gs-table-body')]/tr[1]/td[4]/div/a[2]</msg>
<var>${delete_button}</var>
<arg>${table_rows_element}/td[4]/div/a[2]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-07-03T10:08:11.941524" elapsed="0.000000"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:11.942517" level="INFO">Clicking element 'xpath=//table[contains(@class,'table')][contains(@class,'gs-table')] /tbody[contains(@class,'gs-table-body')]/tr[1]/td[4]/div/a[2]'.</msg>
<arg>${delete_button}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-07-03T10:08:11.941524" elapsed="0.097851"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-07-03T10:08:14.039928" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-07-03T10:08:12.039375" elapsed="2.000553"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${DELETE_CONFIRMATION_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-07-03T10:08:14.040441" elapsed="0.021183"/>
</kw>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:14.192275" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="Delete_Email_Confirmation.png"&gt;&lt;img src="Delete_Email_Confirmation.png" width="800px"&gt;&lt;/a&gt;</msg>
<arg>Delete_Email_Confirmation.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="PASS" start="2025-07-03T10:08:14.061624" elapsed="0.130651"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:14.193793" level="INFO">Clicking element '//*[@id='btnDeleteVendorEmail']'.</msg>
<arg>${DELETE_CONFIRMATION_BTN}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-07-03T10:08:14.192275" elapsed="0.084784"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-07-03T10:08:16.277292" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-07-03T10:08:14.277059" elapsed="2.000233"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:16.306261" level="INFO">Element 'xpath=//div[@id='confirmCall']' is displayed.</msg>
<arg>${NEW_VENDOR_EMAIL_ADDED_CONFIRMATION}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-07-03T10:08:16.278321" elapsed="0.027940"/>
</kw>
<msg time="2025-07-03T10:08:16.306261" level="INFO">${confirmation_visible} = True</msg>
<var>${confirmation_visible}</var>
<arg>SeleniumLibrary.Element Should Be Visible</arg>
<arg>${NEW_VENDOR_EMAIL_ADDED_CONFIRMATION}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-07-03T10:08:16.277292" elapsed="0.028969"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Run Keywords" owner="BuiltIn">
<kw name="Get Text" owner="SeleniumLibrary">
<arg>${NEW_VENDOR_EMAIL_ADDED_CONFIRMATION_MESSAGE}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-07-03T10:08:16.307411" elapsed="0.019854"/>
</kw>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:16.431899" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="Email_Delete_Confirmation.png"&gt;&lt;img src="Email_Delete_Confirmation.png" width="800px"&gt;&lt;/a&gt;</msg>
<arg>Email_Delete_Confirmation.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="PASS" start="2025-07-03T10:08:16.327265" elapsed="0.104634"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:16.432871" level="INFO">Clicking element 'xpath=//div[@id='confirmCall']/descendant::input[@id='MainContent_btnSubmitComplete'][@name='ctl00$MainContent$btnSubmitComplete']'.</msg>
<arg>${NEW_VENDOR_EMAIL_ADDED_OK_BTN}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-07-03T10:08:16.432871" elapsed="0.148306"/>
</kw>
<arg>SeleniumLibrary.Get Text</arg>
<arg>${NEW_VENDOR_EMAIL_ADDED_CONFIRMATION_MESSAGE}</arg>
<arg>AND</arg>
<arg>capture page screenshot</arg>
<arg>Email_Delete_Confirmation.png</arg>
<arg>AND</arg>
<arg>SeleniumLibrary.Click Element</arg>
<arg>${NEW_VENDOR_EMAIL_ADDED_OK_BTN}</arg>
<doc>Executes all the given keywords in a sequence.</doc>
<status status="PASS" start="2025-07-03T10:08:16.306261" elapsed="0.274916"/>
</kw>
<arg>${confirmation_visible}</arg>
<arg>Run Keywords</arg>
<arg>SeleniumLibrary.Get Text</arg>
<arg>${NEW_VENDOR_EMAIL_ADDED_CONFIRMATION_MESSAGE}</arg>
<arg>AND</arg>
<arg>capture page screenshot</arg>
<arg>Email_Delete_Confirmation.png</arg>
<arg>AND</arg>
<arg>SeleniumLibrary.Click Element</arg>
<arg>${NEW_VENDOR_EMAIL_ADDED_OK_BTN}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-07-03T10:08:16.306261" elapsed="0.274916"/>
</kw>
<kw name="Run Keyword Unless" owner="BuiltIn">
<msg time="2025-07-03T10:08:16.582175" level="WARN">Keyword 'BuiltIn.Run Keyword Unless' is deprecated. </msg>
<arg>${confirmation_visible}</arg>
<arg>Run Keywords</arg>
<arg>Log</arg>
<arg>Confirmation dialog not found with expected ID, proceeding with test</arg>
<arg>AND</arg>
<arg>capture page screenshot</arg>
<arg>Email_Delete_Alternative.png</arg>
<doc>*DEPRECATED since RF 5.0. Use Native IF/ELSE or `Run Keyword If` instead.*</doc>
<status status="PASS" start="2025-07-03T10:08:16.582175" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-07-03T10:08:18.583881" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-07-03T10:08:16.583791" elapsed="2.000230"/>
</kw>
<arg>${original_vendor}</arg>
<doc>Delete a vendor email from the Email Management table</doc>
<status status="PASS" start="2025-07-03T10:08:11.927901" elapsed="6.656120"/>
</kw>
<kw name="The user navigates to Admin - Email Management" owner="EmailManagement">
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:18.603686" level="INFO">Current page contains text 'Dashboard'.</msg>
<arg>Dashboard</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-07-03T10:08:18.584899" elapsed="0.018787"/>
</kw>
<msg time="2025-07-03T10:08:18.603686" level="INFO">${on_dashboard} = True</msg>
<var>${on_dashboard}</var>
<arg>SeleniumLibrary.Page Should Contain</arg>
<arg>Dashboard</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-07-03T10:08:18.584899" elapsed="0.018787"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>not ${on_dashboard}</arg>
<arg>Navigate To Dashboard</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-07-03T10:08:18.603686" elapsed="0.001046"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${ADMIN_LINK}</arg>
<arg>10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-07-03T10:08:18.604732" elapsed="0.019079"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:18.637069" level="INFO">Current page contains text 'Dashboard'.</msg>
<arg>Dashboard</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-07-03T10:08:18.623811" elapsed="0.013258"/>
</kw>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:18.743623" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="VMS_Landing_Page.png"&gt;&lt;img src="VMS_Landing_Page.png" width="800px"&gt;&lt;/a&gt;</msg>
<arg>VMS_Landing_Page.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="PASS" start="2025-07-03T10:08:18.637069" elapsed="0.106554"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:18.744508" level="INFO">Clicking element 'xpath=//*[text()[normalize-space(.)='Admin']]'.</msg>
<arg>${ADMIN_LINK}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-07-03T10:08:18.743623" elapsed="0.128452"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:18.872075" level="INFO">Clicking element 'xpath=//a[contains(@class,'nav-link')][text()[normalize-space(.)='Email Management']]'.</msg>
<arg>${EMAIL_MANAGEMENT_LINK}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-07-03T10:08:18.872075" elapsed="0.197580"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:19.090900" level="INFO">Current page contains text 'Email Management'.</msg>
<arg>Email Management</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-07-03T10:08:19.069655" elapsed="0.021245"/>
</kw>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:19.262467" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="Email_Management_Landing_Page.png"&gt;&lt;img src="Email_Management_Landing_Page.png" width="800px"&gt;&lt;/a&gt;</msg>
<arg>Email_Management_Landing_Page.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="PASS" start="2025-07-03T10:08:19.090900" elapsed="0.171567"/>
</kw>
<status status="PASS" start="2025-07-03T10:08:18.584021" elapsed="0.678446"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-07-03T10:08:21.264136" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-07-03T10:08:19.262467" elapsed="2.001669"/>
</kw>
<kw name="Searches for existing user" owner="EmailManagement">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:21.310752" level="INFO">Current page contains text 'Email Management'.</msg>
<arg>Email Management</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-07-03T10:08:21.266820" elapsed="0.043932"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-07-03T10:08:22.313110" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-07-03T10:08:21.311746" elapsed="1.001364"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:22.314676" level="INFO">Typing text '<EMAIL>' into text field 'xpath=//div[@id='VendorEmail']/descendant::input[@id='searchField']'.</msg>
<arg>${VENDOR_SEARCH_INPUT}</arg>
<arg>${SEARCH_DATA}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-07-03T10:08:22.313631" elapsed="0.354870"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-07-03T10:08:24.668964" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-07-03T10:08:22.668501" elapsed="2.000463"/>
</kw>
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:24.724490" level="INFO">Element 'xpath=//table[contains(@class,'table')][contains(@class,'gs-table')]' is displayed.</msg>
<arg>${VMS_USERS_TABLE}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-07-03T10:08:24.668964" elapsed="0.056049"/>
</kw>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-07-03T10:08:24.726041" level="INFO">${table_tbody} = xpath=//table[contains(@class,'table')][contains(@class,'gs-table')] /tbody[contains(@class,'gs-table-body')]</msg>
<var>${table_tbody}</var>
<arg>${VMS_USERS_TABLE}</arg>
<arg>/tbody[contains(@class,'gs-table-body')]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-07-03T10:08:24.725013" elapsed="0.001028"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:24.781792" level="INFO">Element 'xpath=//table[contains(@class,'table')][contains(@class,'gs-table')] /tbody[contains(@class,'gs-table-body')]' is displayed.</msg>
<arg>${table_tbody}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-07-03T10:08:24.728038" elapsed="0.053754"/>
</kw>
<msg time="2025-07-03T10:08:24.781792" level="INFO">${IsElementVisible} = True</msg>
<var>${IsElementVisible}</var>
<arg>Element Should Be Visible</arg>
<arg>${table_tbody}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-07-03T10:08:24.727035" elapsed="0.054757"/>
</kw>
<if>
<branch type="IF" condition="${IsElementVisible} == $True">
<kw name="Log" owner="BuiltIn">
<msg time="2025-07-03T10:08:24.782809" level="INFO">The Vendor: '<EMAIL>' was returned on the search results, which means it exists on VMS.</msg>
<arg>The Vendor: '${SEARCH_DATA}' was returned on the search results, which means it exists on VMS.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-07-03T10:08:24.782809" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-07-03T10:08:24.781792" elapsed="0.001017"/>
</branch>
<branch type="ELSE">
<kw name="Fail" owner="BuiltIn">
<arg>The Vendor: '${SEARCH_DATA}' was not returned on the search results, which means it does not exist on VMS</arg>
<doc>Fails the test with the given message and optionally alters its tags.</doc>
<status status="NOT RUN" start="2025-07-03T10:08:24.782809" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-07-03T10:08:24.782809" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-07-03T10:08:24.781792" elapsed="0.001017"/>
</if>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:24.941423" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="User_Search_thabobenjamin.setuke%40absa.africa.png"&gt;&lt;img src="User_Search_thabobenjamin.setuke%40absa.africa.png" width="800px"&gt;&lt;/a&gt;</msg>
<arg>User_Search_${SEARCH_DATA}.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="PASS" start="2025-07-03T10:08:24.782809" elapsed="0.158614"/>
</kw>
<arg>${original_email}</arg>
<status status="PASS" start="2025-07-03T10:08:21.265201" elapsed="3.676222"/>
</kw>
<kw name="Verify VMS Email Link Deleted" owner="DatabaseConnector">
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Should Not Be Empty" owner="BuiltIn">
<msg time="2025-07-03T10:08:24.942416" level="INFO">Length is 3.</msg>
<arg>${link_id}</arg>
<doc>Verifies that the given item is not empty.</doc>
<status status="PASS" start="2025-07-03T10:08:24.942416" elapsed="0.000000"/>
</kw>
<msg time="2025-07-03T10:08:24.942416" level="INFO">${result1} = True</msg>
<var>${result1}</var>
<arg>Should Not Be Empty</arg>
<arg>${link_id}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-07-03T10:08:24.941423" elapsed="0.000993"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-07-03T10:08:24.942416" level="INFO">${condition1} = True</msg>
<var>${condition1}</var>
<arg>${result1}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-07-03T10:08:24.942416" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>${condition1} == ${FALSE}</arg>
<arg>Fail</arg>
<arg>Please make sure that link_id parameter value is provided!</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-07-03T10:08:24.942416" elapsed="0.000000"/>
</kw>
<kw name="Replace String" owner="String">
<msg time="2025-07-03T10:08:24.942416" level="INFO">${link_id_data} = 580</msg>
<var>${link_id_data}</var>
<arg>${link_id}</arg>
<arg>'</arg>
<arg>''</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<status status="PASS" start="2025-07-03T10:08:24.942416" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-07-03T10:08:24.943496" level="INFO">${query} = SELECT COUNT(*) as record_count FROM [VMS_UAT].[core].[email] WHERE Link = 'LINK_ID_PLACEHOLDER'</msg>
<var>${query}</var>
<arg>${SQL_VMS_EMAIL_LINK_DELETION_CHECK}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-07-03T10:08:24.943416" elapsed="0.000080"/>
</kw>
<kw name="Replace String" owner="String">
<msg time="2025-07-03T10:08:24.943707" level="INFO">${query} = SELECT COUNT(*) as record_count FROM [VMS_UAT].[core].[email] WHERE Link = '580'</msg>
<var>${query}</var>
<arg>${query}</arg>
<arg>LINK_ID_PLACEHOLDER</arg>
<arg>${link_id_data}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<status status="PASS" start="2025-07-03T10:08:24.943496" elapsed="0.000211"/>
</kw>
<kw name="Execute VMS Query" owner="DatabaseConnector">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-07-03T10:08:24.943707" level="INFO">${DB_TYPE} = MSSQL</msg>
<var>${DB_TYPE}</var>
<arg>MSSQL</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-07-03T10:08:24.943707" elapsed="0.000000"/>
</kw>
<kw name="Read Config Property" owner="CommonUtils">
<msg time="2025-07-03T10:08:24.948888" level="INFO">Property value fetched is:  XZAPBCC1SQL1004</msg>
<msg time="2025-07-03T10:08:24.948888" level="INFO">${DB_HOST} = XZAPBCC1SQL1004</msg>
<var>${DB_HOST}</var>
<arg>MS_DB_HOST</arg>
<status status="PASS" start="2025-07-03T10:08:24.943707" elapsed="0.005181"/>
</kw>
<kw name="Read Config Property" owner="CommonUtils">
<msg time="2025-07-03T10:08:24.955433" level="INFO">Property value fetched is:  VMS_UAT</msg>
<msg time="2025-07-03T10:08:24.955433" level="INFO">${DB_NAME} = VMS_UAT</msg>
<var>${DB_NAME}</var>
<arg>MS_DB_SCHEMA</arg>
<status status="PASS" start="2025-07-03T10:08:24.948888" elapsed="0.006545"/>
</kw>
<kw name="Read Config Property" owner="CommonUtils">
<msg time="2025-07-03T10:08:24.960504" level="INFO">Property value fetched is:  apl</msg>
<msg time="2025-07-03T10:08:24.960504" level="INFO">${DB_USER} = apl</msg>
<var>${DB_USER}</var>
<arg>MS_DB_User</arg>
<status status="PASS" start="2025-07-03T10:08:24.955433" elapsed="0.005071"/>
</kw>
<kw name="Read Config Property" owner="CommonUtils">
<msg time="2025-07-03T10:08:24.965541" level="INFO">Property value fetched is:  Pa$$w0rd</msg>
<msg time="2025-07-03T10:08:24.966640" level="INFO">${DB_PASSWORD} = Pa$$w0rd</msg>
<var>${DB_PASSWORD}</var>
<arg>MS_DB_PWD</arg>
<status status="PASS" start="2025-07-03T10:08:24.960504" elapsed="0.006136"/>
</kw>
<kw name="Execute Select Query" owner="DatabaseConnector">
<kw name="Execute Select Statement" owner="Database_Library">
<msg time="2025-07-03T10:08:25.001203" level="INFO">MSSQL XZAPBCC1SQL1004 VMS_UAT apl Pa$$w0rd
Connection established, executing query.</msg>
<msg time="2025-07-03T10:08:25.001203" level="INFO">${results} = [{'record_count': 0}]</msg>
<var>${results}</var>
<arg>${db_type}</arg>
<arg>${host}</arg>
<arg>${database}</arg>
<arg>${username}</arg>
<arg>${password}</arg>
<arg>${query}</arg>
<doc>Executes a SELECT query on either MySQL or MSSQL and returns the results as a dictionary.
Raises an error if the query fails.</doc>
<status status="PASS" start="2025-07-03T10:08:24.968504" elapsed="0.032699"/>
</kw>
<kw name="Return From Keyword" owner="BuiltIn">
<msg time="2025-07-03T10:08:25.003261" level="INFO">Returning from the enclosing user keyword.</msg>
<arg>${results}</arg>
<doc>Returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-07-03T10:08:25.002248" elapsed="0.001306"/>
</kw>
<msg time="2025-07-03T10:08:25.003763" level="INFO">${results} = [{'record_count': 0}]</msg>
<var>${results}</var>
<arg>${DB_TYPE}</arg>
<arg>${DB_HOST}</arg>
<arg>${DB_NAME}</arg>
<arg>${DB_USER}</arg>
<arg>${DB_PASSWORD}</arg>
<arg>${query}</arg>
<doc>Execute a database query against the VMS database and return the results
This keyword handles the connection to the VMS database,
executes the query, and returns the results.</doc>
<status status="PASS" start="2025-07-03T10:08:24.967979" elapsed="0.035784"/>
</kw>
<return>
<value>${results}</value>
<status status="PASS" start="2025-07-03T10:08:25.004571" elapsed="0.000000"/>
</return>
<msg time="2025-07-03T10:08:25.005625" level="INFO">${results} = [{'record_count': 0}]</msg>
<var>${results}</var>
<arg>${query}</arg>
<doc>Execute a query against the VMS database using centralized connection parameters
This keyword handles all database connection details internally.</doc>
<status status="PASS" start="2025-07-03T10:08:24.943707" elapsed="0.061918"/>
</kw>
<kw name="Should Not Be Empty" owner="BuiltIn">
<msg time="2025-07-03T10:08:25.005625" level="INFO">Length is 1.</msg>
<arg>${results}</arg>
<arg>msg=Database query failed</arg>
<doc>Verifies that the given item is not empty.</doc>
<status status="PASS" start="2025-07-03T10:08:25.005625" elapsed="0.000000"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2025-07-03T10:08:25.005625" level="INFO">${first_row} = {'record_count': 0}</msg>
<var>${first_row}</var>
<arg>${results}</arg>
<arg>0</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-07-03T10:08:25.005625" elapsed="0.000000"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<msg time="2025-07-03T10:08:25.006708" level="INFO">${record_count} = 0</msg>
<var>${record_count}</var>
<arg>${first_row}</arg>
<arg>record_count</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="PASS" start="2025-07-03T10:08:25.005625" elapsed="0.001083"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-07-03T10:08:25.006708" level="INFO">Verifying deletion for Link ID: 580, Records found: 0</msg>
<arg>Verifying deletion for Link ID: ${link_id_data}, Records found: ${record_count}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-07-03T10:08:25.006708" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal As Numbers" owner="BuiltIn">
<msg time="2025-07-03T10:08:25.006708" level="INFO">Argument types are:
&lt;class 'int'&gt;
&lt;class 'str'&gt;</msg>
<arg>${record_count}</arg>
<arg>0</arg>
<arg>msg=Email record with Link ID ${link_id_data} still exists in database after deletion</arg>
<doc>Fails if objects are unequal after converting them to real numbers.</doc>
<status status="PASS" start="2025-07-03T10:08:25.006708" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-07-03T10:08:25.006708" level="INFO">Email deletion verification successful - Link ID 580 no longer exists in database</msg>
<arg>Email deletion verification successful - Link ID ${link_id_data} no longer exists in database</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-07-03T10:08:25.006708" elapsed="0.000000"/>
</kw>
<return>
<value>${record_count}</value>
<status status="PASS" start="2025-07-03T10:08:25.006708" elapsed="0.000000"/>
</return>
<arg>${current_link_id}</arg>
<doc>Verify that an email link ID no longer exists in the VMS database after deletion</doc>
<status status="PASS" start="2025-07-03T10:08:24.941423" elapsed="0.065285"/>
</kw>
<kw name="The deleted link ID should not be found on VMS Application" owner="EmailManagement">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:25.023807" level="INFO">Current page contains text 'Email Management'.</msg>
<arg>Email Management</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-07-03T10:08:25.007701" elapsed="0.016106"/>
</kw>
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:25.042928" level="INFO">Element 'xpath=//table[contains(@class,'table')][contains(@class,'gs-table')]' is displayed.</msg>
<arg>${VMS_USERS_TABLE}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-07-03T10:08:25.023807" elapsed="0.019121"/>
</kw>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-07-03T10:08:25.042928" level="INFO">${table_tbody} = xpath=//table[contains(@class,'table')][contains(@class,'gs-table')] /tbody[contains(@class,'gs-table-body')]</msg>
<var>${table_tbody}</var>
<arg>${VMS_USERS_TABLE}</arg>
<arg>/tbody[contains(@class,'gs-table-body')]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-07-03T10:08:25.042928" elapsed="0.001042"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:25.068633" level="INFO">Element 'xpath=//table[contains(@class,'table')][contains(@class,'gs-table')] /tbody[contains(@class,'gs-table-body')]/tr[1]' is displayed.</msg>
<arg>${table_tbody}/tr[1]</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-07-03T10:08:25.044121" elapsed="0.024512"/>
</kw>
<msg time="2025-07-03T10:08:25.068633" level="INFO">${rows_exist} = True</msg>
<var>${rows_exist}</var>
<arg>SeleniumLibrary.Element Should Be Visible</arg>
<arg>${table_tbody}/tr[1]</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-07-03T10:08:25.044121" elapsed="0.024512"/>
</kw>
<kw name="Run Keyword Unless" owner="BuiltIn">
<msg time="2025-07-03T10:08:25.068633" level="WARN">Keyword 'BuiltIn.Run Keyword Unless' is deprecated. </msg>
<arg>${rows_exist}</arg>
<arg>Log</arg>
<arg>UI Verification: Email successfully deleted - no search results found for: ${DELETED_EMAIL}</arg>
<doc>*DEPRECATED since RF 5.0. Use Native IF/ELSE or `Run Keyword If` instead.*</doc>
<status status="PASS" start="2025-07-03T10:08:25.068633" elapsed="0.000705"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Verify Link ID Not In Search Results" owner="EmailManagement">
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:25.080824" level="INFO">${row_count} = 5</msg>
<var>${row_count}</var>
<arg>${table_tbody}/tr</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-07-03T10:08:25.070374" elapsed="0.010450"/>
</kw>
<for flavor="IN RANGE">
<iter>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-07-03T10:08:25.082832" level="INFO">${table_rows_element} = xpath=//table[contains(@class,'table')][contains(@class,'gs-table')] /tbody[contains(@class,'gs-table-body')]/tr[1]</msg>
<var>${table_rows_element}</var>
<arg>${table_tbody}/tr[${i}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-07-03T10:08:25.081822" elapsed="0.001010"/>
</kw>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-07-03T10:08:25.082832" level="INFO">${id_element} = xpath=//table[contains(@class,'table')][contains(@class,'gs-table')] /tbody[contains(@class,'gs-table-body')]/tr[1]/td[1]</msg>
<var>${id_element}</var>
<arg>${table_rows_element}/td[1]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-07-03T10:08:25.082832" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:25.108233" level="INFO">Element 'xpath=//table[contains(@class,'table')][contains(@class,'gs-table')] /tbody[contains(@class,'gs-table-body')]/tr[1]/td[1]' is displayed.</msg>
<arg>${id_element}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-07-03T10:08:25.082832" elapsed="0.025401"/>
</kw>
<msg time="2025-07-03T10:08:25.108233" level="INFO">${row_exists} = True</msg>
<var>${row_exists}</var>
<arg>SeleniumLibrary.Element Should Be Visible</arg>
<arg>${id_element}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-07-03T10:08:25.082832" elapsed="0.025401"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Verify Single Link ID Not Deleted" owner="EmailManagement">
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:25.130503" level="INFO">${current_id} = 581</msg>
<var>${current_id}</var>
<arg>${id_element}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-07-03T10:08:25.109253" elapsed="0.021250"/>
</kw>
<kw name="Strip String" owner="String">
<msg time="2025-07-03T10:08:25.131505" level="INFO">${current_id_trimmed} = 581</msg>
<var>${current_id_trimmed}</var>
<arg>${current_id}</arg>
<doc>Remove leading and/or trailing whitespaces from the given string.</doc>
<status status="PASS" start="2025-07-03T10:08:25.130503" elapsed="0.001002"/>
</kw>
<kw name="Should Not Be Equal As Strings" owner="BuiltIn">
<arg>${current_id_trimmed}</arg>
<arg>${deleted_link_id}</arg>
<arg>msg=Deleted link ID ${deleted_link_id} still found in search results</arg>
<doc>Fails if objects are equal after converting them to strings.</doc>
<status status="PASS" start="2025-07-03T10:08:25.131505" elapsed="0.000000"/>
</kw>
<arg>${id_element}</arg>
<arg>${deleted_link_id}</arg>
<doc>Helper keyword to verify a single link ID is not the deleted one</doc>
<status status="PASS" start="2025-07-03T10:08:25.108233" elapsed="0.023272"/>
</kw>
<arg>${row_exists}</arg>
<arg>Verify Single Link ID Not Deleted</arg>
<arg>${id_element}</arg>
<arg>${deleted_link_id}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-07-03T10:08:25.108233" elapsed="0.023272"/>
</kw>
<var name="${i}">1</var>
<status status="PASS" start="2025-07-03T10:08:25.081822" elapsed="0.049683"/>
</iter>
<iter>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-07-03T10:08:25.132261" level="INFO">${table_rows_element} = xpath=//table[contains(@class,'table')][contains(@class,'gs-table')] /tbody[contains(@class,'gs-table-body')]/tr[2]</msg>
<var>${table_rows_element}</var>
<arg>${table_tbody}/tr[${i}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-07-03T10:08:25.132261" elapsed="0.000000"/>
</kw>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-07-03T10:08:25.132261" level="INFO">${id_element} = xpath=//table[contains(@class,'table')][contains(@class,'gs-table')] /tbody[contains(@class,'gs-table-body')]/tr[2]/td[1]</msg>
<var>${id_element}</var>
<arg>${table_rows_element}/td[1]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-07-03T10:08:25.132261" elapsed="0.001029"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:25.156146" level="INFO">Element 'xpath=//table[contains(@class,'table')][contains(@class,'gs-table')] /tbody[contains(@class,'gs-table-body')]/tr[2]/td[1]' is displayed.</msg>
<arg>${id_element}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-07-03T10:08:25.133290" elapsed="0.022856"/>
</kw>
<msg time="2025-07-03T10:08:25.156146" level="INFO">${row_exists} = True</msg>
<var>${row_exists}</var>
<arg>SeleniumLibrary.Element Should Be Visible</arg>
<arg>${id_element}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-07-03T10:08:25.133290" elapsed="0.022856"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Verify Single Link ID Not Deleted" owner="EmailManagement">
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:25.178152" level="INFO">${current_id} = 582</msg>
<var>${current_id}</var>
<arg>${id_element}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-07-03T10:08:25.157218" elapsed="0.020934"/>
</kw>
<kw name="Strip String" owner="String">
<msg time="2025-07-03T10:08:25.178152" level="INFO">${current_id_trimmed} = 582</msg>
<var>${current_id_trimmed}</var>
<arg>${current_id}</arg>
<doc>Remove leading and/or trailing whitespaces from the given string.</doc>
<status status="PASS" start="2025-07-03T10:08:25.178152" elapsed="0.000000"/>
</kw>
<kw name="Should Not Be Equal As Strings" owner="BuiltIn">
<arg>${current_id_trimmed}</arg>
<arg>${deleted_link_id}</arg>
<arg>msg=Deleted link ID ${deleted_link_id} still found in search results</arg>
<doc>Fails if objects are equal after converting them to strings.</doc>
<status status="PASS" start="2025-07-03T10:08:25.178152" elapsed="0.000000"/>
</kw>
<arg>${id_element}</arg>
<arg>${deleted_link_id}</arg>
<doc>Helper keyword to verify a single link ID is not the deleted one</doc>
<status status="PASS" start="2025-07-03T10:08:25.157218" elapsed="0.021934"/>
</kw>
<arg>${row_exists}</arg>
<arg>Verify Single Link ID Not Deleted</arg>
<arg>${id_element}</arg>
<arg>${deleted_link_id}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-07-03T10:08:25.156146" elapsed="0.023006"/>
</kw>
<var name="${i}">2</var>
<status status="PASS" start="2025-07-03T10:08:25.132261" elapsed="0.046891"/>
</iter>
<iter>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-07-03T10:08:25.179152" level="INFO">${table_rows_element} = xpath=//table[contains(@class,'table')][contains(@class,'gs-table')] /tbody[contains(@class,'gs-table-body')]/tr[3]</msg>
<var>${table_rows_element}</var>
<arg>${table_tbody}/tr[${i}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-07-03T10:08:25.179152" elapsed="0.000000"/>
</kw>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-07-03T10:08:25.179152" level="INFO">${id_element} = xpath=//table[contains(@class,'table')][contains(@class,'gs-table')] /tbody[contains(@class,'gs-table-body')]/tr[3]/td[1]</msg>
<var>${id_element}</var>
<arg>${table_rows_element}/td[1]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-07-03T10:08:25.179152" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:25.207629" level="INFO">Element 'xpath=//table[contains(@class,'table')][contains(@class,'gs-table')] /tbody[contains(@class,'gs-table-body')]/tr[3]/td[1]' is displayed.</msg>
<arg>${id_element}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-07-03T10:08:25.179152" elapsed="0.028477"/>
</kw>
<msg time="2025-07-03T10:08:25.207629" level="INFO">${row_exists} = True</msg>
<var>${row_exists}</var>
<arg>SeleniumLibrary.Element Should Be Visible</arg>
<arg>${id_element}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-07-03T10:08:25.179152" elapsed="0.028477"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Verify Single Link ID Not Deleted" owner="EmailManagement">
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:25.226629" level="INFO">${current_id} = 583</msg>
<var>${current_id}</var>
<arg>${id_element}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-07-03T10:08:25.208624" elapsed="0.018005"/>
</kw>
<kw name="Strip String" owner="String">
<msg time="2025-07-03T10:08:25.227626" level="INFO">${current_id_trimmed} = 583</msg>
<var>${current_id_trimmed}</var>
<arg>${current_id}</arg>
<doc>Remove leading and/or trailing whitespaces from the given string.</doc>
<status status="PASS" start="2025-07-03T10:08:25.226629" elapsed="0.000997"/>
</kw>
<kw name="Should Not Be Equal As Strings" owner="BuiltIn">
<arg>${current_id_trimmed}</arg>
<arg>${deleted_link_id}</arg>
<arg>msg=Deleted link ID ${deleted_link_id} still found in search results</arg>
<doc>Fails if objects are equal after converting them to strings.</doc>
<status status="PASS" start="2025-07-03T10:08:25.227626" elapsed="0.000000"/>
</kw>
<arg>${id_element}</arg>
<arg>${deleted_link_id}</arg>
<doc>Helper keyword to verify a single link ID is not the deleted one</doc>
<status status="PASS" start="2025-07-03T10:08:25.208624" elapsed="0.019002"/>
</kw>
<arg>${row_exists}</arg>
<arg>Verify Single Link ID Not Deleted</arg>
<arg>${id_element}</arg>
<arg>${deleted_link_id}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-07-03T10:08:25.208624" elapsed="0.019002"/>
</kw>
<var name="${i}">3</var>
<status status="PASS" start="2025-07-03T10:08:25.179152" elapsed="0.048474"/>
</iter>
<iter>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-07-03T10:08:25.227626" level="INFO">${table_rows_element} = xpath=//table[contains(@class,'table')][contains(@class,'gs-table')] /tbody[contains(@class,'gs-table-body')]/tr[4]</msg>
<var>${table_rows_element}</var>
<arg>${table_tbody}/tr[${i}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-07-03T10:08:25.227626" elapsed="0.000000"/>
</kw>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-07-03T10:08:25.227626" level="INFO">${id_element} = xpath=//table[contains(@class,'table')][contains(@class,'gs-table')] /tbody[contains(@class,'gs-table-body')]/tr[4]/td[1]</msg>
<var>${id_element}</var>
<arg>${table_rows_element}/td[1]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-07-03T10:08:25.227626" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:25.246201" level="INFO">Element 'xpath=//table[contains(@class,'table')][contains(@class,'gs-table')] /tbody[contains(@class,'gs-table-body')]/tr[4]/td[1]' is displayed.</msg>
<arg>${id_element}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-07-03T10:08:25.228673" elapsed="0.017528"/>
</kw>
<msg time="2025-07-03T10:08:25.246201" level="INFO">${row_exists} = True</msg>
<var>${row_exists}</var>
<arg>SeleniumLibrary.Element Should Be Visible</arg>
<arg>${id_element}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-07-03T10:08:25.228673" elapsed="0.017528"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Verify Single Link ID Not Deleted" owner="EmailManagement">
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:25.271846" level="INFO">${current_id} = 584</msg>
<var>${current_id}</var>
<arg>${id_element}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-07-03T10:08:25.248700" elapsed="0.023146"/>
</kw>
<kw name="Strip String" owner="String">
<msg time="2025-07-03T10:08:25.271846" level="INFO">${current_id_trimmed} = 584</msg>
<var>${current_id_trimmed}</var>
<arg>${current_id}</arg>
<doc>Remove leading and/or trailing whitespaces from the given string.</doc>
<status status="PASS" start="2025-07-03T10:08:25.271846" elapsed="0.000000"/>
</kw>
<kw name="Should Not Be Equal As Strings" owner="BuiltIn">
<arg>${current_id_trimmed}</arg>
<arg>${deleted_link_id}</arg>
<arg>msg=Deleted link ID ${deleted_link_id} still found in search results</arg>
<doc>Fails if objects are equal after converting them to strings.</doc>
<status status="PASS" start="2025-07-03T10:08:25.271846" elapsed="0.000000"/>
</kw>
<arg>${id_element}</arg>
<arg>${deleted_link_id}</arg>
<doc>Helper keyword to verify a single link ID is not the deleted one</doc>
<status status="PASS" start="2025-07-03T10:08:25.246201" elapsed="0.025645"/>
</kw>
<arg>${row_exists}</arg>
<arg>Verify Single Link ID Not Deleted</arg>
<arg>${id_element}</arg>
<arg>${deleted_link_id}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-07-03T10:08:25.246201" elapsed="0.025645"/>
</kw>
<var name="${i}">4</var>
<status status="PASS" start="2025-07-03T10:08:25.227626" elapsed="0.044220"/>
</iter>
<iter>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-07-03T10:08:25.271846" level="INFO">${table_rows_element} = xpath=//table[contains(@class,'table')][contains(@class,'gs-table')] /tbody[contains(@class,'gs-table-body')]/tr[5]</msg>
<var>${table_rows_element}</var>
<arg>${table_tbody}/tr[${i}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-07-03T10:08:25.271846" elapsed="0.000000"/>
</kw>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-07-03T10:08:25.271846" level="INFO">${id_element} = xpath=//table[contains(@class,'table')][contains(@class,'gs-table')] /tbody[contains(@class,'gs-table-body')]/tr[5]/td[1]</msg>
<var>${id_element}</var>
<arg>${table_rows_element}/td[1]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-07-03T10:08:25.271846" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:25.301921" level="INFO">Element 'xpath=//table[contains(@class,'table')][contains(@class,'gs-table')] /tbody[contains(@class,'gs-table-body')]/tr[5]/td[1]' is displayed.</msg>
<arg>${id_element}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-07-03T10:08:25.271846" elapsed="0.030075"/>
</kw>
<msg time="2025-07-03T10:08:25.301921" level="INFO">${row_exists} = True</msg>
<var>${row_exists}</var>
<arg>SeleniumLibrary.Element Should Be Visible</arg>
<arg>${id_element}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-07-03T10:08:25.271846" elapsed="0.030075"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Verify Single Link ID Not Deleted" owner="EmailManagement">
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:25.331027" level="INFO">${current_id} = 585</msg>
<var>${current_id}</var>
<arg>${id_element}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-07-03T10:08:25.302943" elapsed="0.028084"/>
</kw>
<kw name="Strip String" owner="String">
<msg time="2025-07-03T10:08:25.332576" level="INFO">${current_id_trimmed} = 585</msg>
<var>${current_id_trimmed}</var>
<arg>${current_id}</arg>
<doc>Remove leading and/or trailing whitespaces from the given string.</doc>
<status status="PASS" start="2025-07-03T10:08:25.331547" elapsed="0.001029"/>
</kw>
<kw name="Should Not Be Equal As Strings" owner="BuiltIn">
<arg>${current_id_trimmed}</arg>
<arg>${deleted_link_id}</arg>
<arg>msg=Deleted link ID ${deleted_link_id} still found in search results</arg>
<doc>Fails if objects are equal after converting them to strings.</doc>
<status status="PASS" start="2025-07-03T10:08:25.333574" elapsed="0.000999"/>
</kw>
<arg>${id_element}</arg>
<arg>${deleted_link_id}</arg>
<doc>Helper keyword to verify a single link ID is not the deleted one</doc>
<status status="PASS" start="2025-07-03T10:08:25.301921" elapsed="0.032652"/>
</kw>
<arg>${row_exists}</arg>
<arg>Verify Single Link ID Not Deleted</arg>
<arg>${id_element}</arg>
<arg>${deleted_link_id}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-07-03T10:08:25.301921" elapsed="0.032652"/>
</kw>
<var name="${i}">5</var>
<status status="PASS" start="2025-07-03T10:08:25.271846" elapsed="0.062727"/>
</iter>
<var>${i}</var>
<value>1</value>
<value>${row_count + 1}</value>
<status status="PASS" start="2025-07-03T10:08:25.080824" elapsed="0.254750"/>
</for>
<kw name="Log" owner="BuiltIn">
<msg time="2025-07-03T10:08:25.336571" level="INFO">Link ID verification completed - 580 not found in any search results</msg>
<arg>Link ID verification completed - ${deleted_link_id} not found in any search results</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-07-03T10:08:25.335574" elapsed="0.000997"/>
</kw>
<arg>${table_tbody}</arg>
<arg>${DELETED_LINK_ID}</arg>
<doc>Helper keyword to verify that a deleted link ID is not in the search results</doc>
<status status="PASS" start="2025-07-03T10:08:25.070374" elapsed="0.266197"/>
</kw>
<arg>${rows_exist}</arg>
<arg>Verify Link ID Not In Search Results</arg>
<arg>${table_tbody}</arg>
<arg>${DELETED_LINK_ID}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-07-03T10:08:25.069338" elapsed="0.267233"/>
</kw>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:25.484245" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="Email_Deletion_UI_Verified.png"&gt;&lt;img src="Email_Deletion_UI_Verified.png" width="800px"&gt;&lt;/a&gt;</msg>
<arg>Email_Deletion_UI_Verified.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="PASS" start="2025-07-03T10:08:25.337573" elapsed="0.146672"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-07-03T10:08:25.484245" level="INFO">UI deletion verification completed for email: <EMAIL> (Link ID: 580)</msg>
<arg>UI deletion verification completed for email: ${DELETED_EMAIL} (Link ID: ${DELETED_LINK_ID})</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-07-03T10:08:25.484245" elapsed="0.000000"/>
</kw>
<arg>${original_email}</arg>
<arg>${current_link_id}</arg>
<doc>Verify that the specific deleted link ID is no longer found in the UI search results</doc>
<status status="PASS" start="2025-07-03T10:08:25.007701" elapsed="0.477544"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-07-03T10:08:25.485245" level="INFO">===== EMAIL DELETION TEST COMPLETED SUCCESSFULLY =====</msg>
<arg>===== EMAIL DELETION TEST COMPLETED SUCCESSFULLY =====</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-07-03T10:08:25.485245" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-07-03T10:08:25.485245" level="INFO">Original Link ID: 603</msg>
<arg>Original Link ID: ${original_link_id}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-07-03T10:08:25.485245" elapsed="0.001051"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-07-03T10:08:25.486296" level="INFO">Current Link ID: 580</msg>
<arg>Current Link ID: ${current_link_id}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-07-03T10:08:25.486296" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-07-03T10:08:25.486296" level="INFO">Original Vendor: BMS                                </msg>
<arg>Original Vendor: ${original_vendor}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-07-03T10:08:25.486296" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-07-03T10:08:25.486296" level="INFO">Original Email: <EMAIL></msg>
<arg>Original Email: ${original_email}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-07-03T10:08:25.486296" elapsed="0.001001"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-07-03T10:08:25.487297" level="INFO">Verification Status: Email deleted and verified in both UI and Database</msg>
<arg>Verification Status: Email deleted and verified in both UI and Database</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-07-03T10:08:25.487297" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-07-03T10:08:25.487297" level="INFO">Database Verification: Link ID 580 no longer exists in [VMS_UAT].[core].[email] table</msg>
<arg>Database Verification: Link ID ${current_link_id} no longer exists in [VMS_UAT].[core].[email] table</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-07-03T10:08:25.487297" elapsed="0.000301"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-07-03T10:08:25.487598" level="INFO">UI Verification: Link ID 580 not found in Email Management search results</msg>
<arg>UI Verification: Link ID ${current_link_id} not found in Email Management search results</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-07-03T10:08:25.487598" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-07-03T10:08:25.487598" level="INFO">===== TEST SUMMARY: DELETION SUCCESSFUL =====</msg>
<arg>===== TEST SUMMARY: DELETION SUCCESSFUL =====</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-07-03T10:08:25.487598" elapsed="0.000000"/>
</kw>
<return>
<value>${original_link_id}</value>
<value>${original_vendor}</value>
<value>${original_email}</value>
<status status="PASS" start="2025-07-03T10:08:25.487598" elapsed="0.000000"/>
</return>
<doc>Get a random email from database, delete it, and verify the deletion</doc>
<status status="PASS" start="2025-07-03T10:08:08.007450" elapsed="17.480148"/>
</kw>
<arg>Validate the delete functionality for vendor emails in Email Management.</arg>
<arg>VMS_UAT</arg>
<status status="PASS" start="2025-07-03T10:07:24.881388" elapsed="60.606210"/>
</kw>
<kw name="The user logs out of VMS" owner="Logout" type="TEARDOWN">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_LOGOUT_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-07-03T10:08:25.490252" elapsed="0.053450"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------------------ Logging out of VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-07-03T10:08:25.544756" elapsed="0.000991"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-07-03T10:08:25.546902" level="INFO">Clicking button 'name=ctl00$btnLogout'.</msg>
<arg>${VMS_LOGOUT_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-07-03T10:08:25.546902" elapsed="0.323550"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-07-03T10:08:28.871049" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-07-03T10:08:25.870452" elapsed="3.000597"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2025-07-03T10:08:28.871620" elapsed="3.658651"/>
</kw>
<status status="PASS" start="2025-07-03T10:08:25.488442" elapsed="7.042814"/>
</kw>
<doc>Validate the delete functionality for vendor emails in Email Management.</doc>
<tag>HEALTHCHECK_STATUS</tag>
<tag>Login</tag>
<tag>VMS_HEALTHCHECK</tag>
<status status="PASS" start="2025-07-03T10:07:24.880393" elapsed="67.650863"/>
</test>
<doc>Validate Delete Link functionality in Email Management</doc>
<status status="PASS" start="2025-07-03T10:07:18.740116" elapsed="73.794074"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="1" fail="0" skip="0">HEALTHCHECK_STATUS</stat>
<stat pass="1" fail="0" skip="0">Login</stat>
<stat pass="1" fail="0" skip="0">VMS_HEALTHCHECK</stat>
</tag>
<suite>
<stat name="VMS Portal" id="s1" pass="1" fail="0" skip="0">VMS Portal</stat>
</suite>
</statistics>
<errors>
<msg time="2025-07-03T10:07:22.922879" level="ERROR">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\SetEnvironmentVariales.robot' on line 14: Importing library 'C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py' failed: ModuleNotFoundError: No module named 'acintegration'
Traceback (most recent call last):
  File "C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py", line 1, in &lt;module&gt;
    from acintegration.QMetryIntegration import QMetryIntegration
PYTHONPATH:
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\robot.exe
  C:\Alternative
  C:\Alternative\Alternative_Physical_Channels_QA
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages</msg>
<msg time="2025-07-03T10:07:22.937425" level="WARN">Imported library 'C:\Alternative\Alternative_Physical_Channels_QA\future_fit_architecture_portal_BinTables\keywords\controllers\resources\RestRequestsUnMarshal.py' contains no keywords.</msg>
<msg time="2025-07-03T10:07:24.774775" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\common_keywords.robot' on line 117: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-07-03T10:07:24.775780" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\common_keywords.robot' on line 137: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-07-03T10:07:24.858375" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\DatabaseConnector.robot' on line 328: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-07-03T10:07:24.858375" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\DatabaseConnector.robot' on line 356: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-07-03T10:07:26.136016" level="WARN">There was error during termination of process</msg>
<msg time="2025-07-03T10:07:26.145018" level="WARN">exexcutable_path is being deprecated. Please use service to configure the driver's executable_path as per documentation.</msg>
<msg time="2025-07-03T10:08:16.582175" level="WARN">Keyword 'BuiltIn.Run Keyword Unless' is deprecated. </msg>
<msg time="2025-07-03T10:08:25.068633" level="WARN">Keyword 'BuiltIn.Run Keyword Unless' is deprecated. </msg>
</errors>
</robot>
