*** Settings ***
#Author Name               : THab<PERSON>
#Email Address             : <EMAIL>

Default Tags                                        VMS_HEALTHCHECK     HEALTHCHECK_STATUS   Login
Suite Setup                                         Set up environment variables
Test Teardown                                       The user logs out of VMS
Documentation                                       Add new User to VMS

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../common_utilities/Login.robot
Resource                                            ../../../common_utilities/Logout.robot
Resource                                            ../../keywords/common/common_keywords.robot
Resource                                            ../../keywords/VMSPage/EmailManagement.robot

*Variables*


*** Keywords ***
VMS Vendor Creation - Negative Testing
    [Arguments]  ${DOCUMENTATION}       ${TEST_ENVIRONMENT}     ${VENDOR_NAME}       ${EMAIL}      ${EXPECTED_ERROR}
    Set Test Documentation  ${DOCUMENTATION}
    Given The user logs into the VMS Web Application     ${TEST_ENVIRONMENT}
    When The user navigates to Admin - Email Management
    And The user Adds a new VMS Vendor Email while leaving some fields blank    ${VENDOR_NAME}       ${EMAIL}
    Then The expected error message must be displayed   ${EXPECTED_ERROR}




| *Test Case*                                                                                                        |      *DOCUMENTATION*                                          | *TEST_ENVIRONMENT*   |  *VENDOR_NAME*  |             *EMAIL*                  |    *EXPECTED_ERROR*              |
# | Validate blank fields for adding a new vendor email- Email Management: Create a VMS Vendor Email, do not populate the 'Vendor' input field.  | VMS Vendor Creation - Negative Testing     | Create a VMS Vendor without populating the 'Vendor' input.    |    VMS_UAT           |                 |  <EMAIL>    |   Please fill out this field.    |
| Validate blank fields for adding a new vendor email- Email Management: Create a VMS Vendor Email, do not populate the 'Email' input field.   | VMS Vendor Creation - Negative Testing     | Create a VMS Vendor without populating the 'Email' input.     |    VMS_UAT           |   Test Vendor   |                                      |   Please fill out this field.    |
