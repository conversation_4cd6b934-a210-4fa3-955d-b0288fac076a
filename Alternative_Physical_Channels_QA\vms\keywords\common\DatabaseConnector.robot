*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Documentation  Bin Tables database connector keywords
#***********************************EXTERNAL LIBRIRIES***************************************
Library                     Collections
Library                     String
Library                     ../../../common_utilities/Database_Library.py
Library                     ../../../common_utilities/CommonUtils.py
Variables                   ../../utility/SQLVariables.py

#***********************************PROJECT RESOURCES***************************************


#***********************************PROJECT VARIABLES***************************************

*** Variables ***
${DB_TYPE}          mysql      # or 'mssql'
${DB_HOST}          oss-vip-02186.corp.dsarena.com
${DB_NAME}          appdb
${DB_USER}          app_account
${DB_PASSWORD}      CcQZglNU0E

${MS_DB_TYPE}          mssql      # or 'mssql'
${MS_DB_HOST}          XZAPBCC1SQL1004
${MS_DB_NAME}          VMS_UAT
${MS_DB_USER}          apl
${MS_DB_PASSWORD}      Pa$$w0rd

# VMS-specific database connection variables
${VMS_DB_TYPE}         mssql
${VMS_DB_HOST}         XZAPBCC1SQL1004
${VMS_DB_NAME}         VMS_UAT
${VMS_DB_USER}         apl
${VMS_DB_PASSWORD}     Pa$$w0rd

#*** Test Cases ***


*** Keywords ***

# VMS-specific database keywords

Get VMS Gasper Details
    [Documentation]    Get total count of VMS Gasper details from the database

    # Use centralized SQL query
    ${row_count_query}   Set Variable    ${SQL_GET_VMS_GASPER_DETAILS}

    # Execute query using centralized database connection
    ${results}=    Execute VMS Query    ${row_count_query}

    RETURN    ${results}

Get VMS Gasper Details Using ATM ID
    [Arguments]    ${ATM_Number}
    [Documentation]    Get VMS Gasper details for a specific ATM ID

    # Verify that all parameters are supplied
    Run Keyword If    '${ATM_Number}' == '${EMPTY}'
    ...     Fail    Please make sure that values of all parameters are provided!

    # Use centralized SQL query
    ${ATM_Details_query}   Set Variable    ${SQL_GET_VMS_GASPER_DETAILS_USING_ATM_ID}
    ${ATM_Details_query}=  Replace String      ${ATM_Details_query}       atm_id     ${ATM_Number}

    # Execute query using centralized database connection
    ${results}=    Execute VMS Query    ${ATM_Details_query}

    RETURN    ${results}

Get VMS Email Details
    [Arguments]    ${link_id}
    [Documentation]    Get email details from the VMS database by link ID

    # Verify that all parameters are supplied
    ${result1}=    Run Keyword And Return Status    Should Not Be Empty    ${link_id}
    ${condition1}=    Set Variable    ${result1}
    Run Keyword If    ${condition1} == ${FALSE}
    ...     Fail    Please make sure that link_id parameter value is provided!

    # Escape single quotes in the link_id
    ${link_id_data}=    Replace String    ${link_id}    '    ''

    # Construct the query using SQL variable
    ${query}=    Set Variable    ${SQL_VMS_EMAIL_DETAILS_BY_LINK_ID}
    ${query}=    Replace String    ${query}    LINK_ID_PLACEHOLDER    ${link_id_data}

    # Execute the query using centralized database connection
    ${results}=    Execute VMS Query    ${query}

    RETURN    ${results}

Get VMS Vendor Details
    [Arguments]    ${linkId}
    [Documentation]    Get user details from the VMS database by username

    # Verify that all parameters are supplied
    ${result1}=    Run Keyword And Return Status    Should Not Be Empty    ${linkId}
    ${condition1}=    Set Variable    ${result1}
    Run Keyword If    ${condition1} == ${FALSE}
    ...     Fail    Please make sure that link id parameter value is provided!

    # Escape single quotes in the username
    ${linkId}=    Replace String    ${linkId}    '    ''

    # Construct the query using SQL variable
    ${my_query}   Set Variable    ${SQL_VMS_EMAIL_DETAILS}
    ${my_query}=    Replace String    ${my_query}       LINK_ID    ${linkId}

    Log Many    ${my_query}

    # Execute query using centralized database connection
    ${results}=    Execute VMS Query    ${my_query}

    Log Many    ${results}
    RETURN      ${results}

Get Random VMS Email
    [Documentation]    Get a random email from the VMS database for testing purposes

    # Construct the query using SQL variable
    ${my_query}   Set Variable    ${SQL_GET_RANDOM_VMS_EMAIL}

    Log Many    ${my_query}

    # Execute query using centralized database connection
    ${results}=    Execute VMS Query    ${my_query}

    Log Many    ${results}

    # Verify that we got a result
    Should Not Be Empty    ${results}    msg=No emails found in the database

    # Return the first (and only) result
    ${random_email_record}=    Get From List    ${results}    0
    RETURN      ${random_email_record}

Verify VMS Email Link Deleted
    [Arguments]    ${link_id}
    [Documentation]    Verify that an email link ID no longer exists in the VMS database after deletion

    # Verify that all parameters are supplied
    ${result1}=    Run Keyword And Return Status    Should Not Be Empty    ${link_id}
    ${condition1}=    Set Variable    ${result1}
    Run Keyword If    ${condition1} == ${FALSE}
    ...     Fail    Please make sure that link_id parameter value is provided!

    # Escape single quotes in the link_id
    ${link_id_data}=    Replace String    ${link_id}    '    ''

    # Construct the query using SQL variable
    ${query}=    Set Variable    ${SQL_VMS_EMAIL_LINK_DELETION_CHECK}
    ${query}=    Replace String    ${query}    LINK_ID_PLACEHOLDER    ${link_id_data}

    # Execute the query using centralized database connection
    ${results}=    Execute VMS Query    ${query}


    # Verify that we got a result
    Should Not Be Empty    ${results}    msg=Database query failed

    # Get the count from the result
    ${first_row}=    Get From List    ${results}    0
    ${record_count}=    Get From Dictionary    ${first_row}    record_count

    # Log the verification attempt
    Log    Verifying deletion for Link ID: ${link_id_data}, Records found: ${record_count}

    # Verify that no records exist (count should be 0)
    Should Be Equal As Numbers    ${record_count}    0    msg=Email record with Link ID ${link_id_data} still exists in database after deletion

    # Log success
    Log    Email deletion verification successful - Link ID ${link_id_data} no longer exists in database

    RETURN    ${record_count}

Verify VMS Email Link Updated
    [Arguments]    ${link_id}    ${expected_email}
    [Documentation]    Verify that an email link ID contains the expected updated email address

    # Verify that all parameters are supplied
    ${result1}=    Run Keyword And Return Status    Should Not Be Empty    ${link_id}
    ${result2}=    Run Keyword And Return Status    Should Not Be Empty    ${expected_email}
    Run Keyword If    ${result1} == ${FALSE} or ${result2} == ${FALSE}
    ...     Fail    Please make sure that both link_id and expected_email parameters are provided!

    # Get the current email details for this link ID
    ${email_details}=    Get VMS Email Details    ${link_id}

    # Verify that we got a result
    Should Not Be Empty    ${email_details}    msg=No email record found for Link ID ${link_id}

    # Get the email from the result
    ${first_row}=    Get From List    ${email_details}    0
    ${actual_email}=    Get From Dictionary    ${first_row}    Email
    ${actual_vendor}=    Get From Dictionary    ${first_row}    Vendor

    # Log the verification attempt
    Log    Verifying update for Link ID: ${link_id}
    Log    Expected Email: ${expected_email}
    Log    Actual Email: ${actual_email}
    Log    Vendor: ${actual_vendor}

    # Verify that the email matches the expected value
    Should Be Equal As Strings    ${actual_email}    ${expected_email}    msg=Email update verification failed. Link ID ${link_id} contains '${actual_email}' but expected '${expected_email}'

    # Log success
    Log    Email update verification successful - Link ID ${link_id} now contains email ${expected_email}

    RETURN    ${actual_email}    ${actual_vendor}



Execute VMS Query
    [Arguments]    ${query}
    [Documentation]    Execute a query against the VMS database using centralized connection parameters
    ...              This keyword handles all database connection details internally.

    # Get database connection parameters from config
    ${DB_TYPE}=    Set Variable    MSSQL
    ${DB_HOST}=    Read Config Property    MS_DB_HOST
    ${DB_NAME}=    Read Config Property    MS_DB_SCHEMA
    ${DB_USER}=    Read Config Property    MS_DB_User
    ${DB_PASSWORD}=    Read Config Property    MS_DB_PWD

    # Execute query using the existing Execute Select Query keyword
    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${query}

    RETURN    ${results}

Execute Select Query
    [Arguments]    ${db_type}    ${host}    ${database}    ${username}    ${password}    ${query}
    [Documentation]    Execute a database query against the VMS database and return the results
    ...              This keyword handles the connection to the VMS database,
    ...              executes the query, and returns the results.

    ${results}=    Execute Select Statement    ${db_type}    ${host}    ${database}    ${username}    ${password}    ${query}
    Return From Keyword    ${results}

Execute Select Query with parameters
    [Arguments]    ${db_type}    ${host}    ${database}    ${username}    ${password}    ${query}   ${params}
    ${results}=    Execute Select Statement with parameters   ${db_type}    ${host}    ${database}    ${username}    ${password}    ${query}   ${params}
    Return From Keyword    ${results}


Get Column Data By Name
    [Arguments]     ${results_row}  ${col_name}

    ${col_data}=    Get From Dictionary    ${results_row}    ${col_name}
    Log    Column data: ${col_data}
    RETURN  ${col_data}

Get Row By Column Value From DB results
    [Arguments]  ${db_results}  ${column_name}  ${value_to_find}
    FOR  ${row}  IN  @{db_results}
      ${col_data}=      Set Variable    ${row[${column_name}]}
      Run Keyword If  "${col_data}" == "${value_to_find}"  Return From Keyword  ${row}
    END
    Run Keyword And Continue On Failure     Fail  No row found with ${column_name} = ${value_to_find}
    RETURN  None

Get Rows By Column Values
    [Arguments]    ${data}    ${conditions}
    # Initialize an empty list to store the matching rows
    ${matching_rows}=    Create List

    # Loop through each row in the data
    FOR    ${row}    IN    @{data}
        # Assume the row matches the condition
        ${matching_row_found}=    Set Variable    ${True}
        ${keys}=    Evaluate    list(${conditions}.keys())    # Get the keys as a list

        # Loop through each condition and check if the row satisfies it
        FOR    ${key}    IN    @{keys}
            ${expected_value}=    Get From Dictionary    ${conditions}    ${key}
            ${expected_value}=   Convert to String      ${expected_value}
            Log    Key: ${key}, Value: ${expected_value}

            ${is_valid_date}=    Check If Valid Date    ${expected_value}
            IF    ${is_valid_date}
                ${converted_date}=      Convert to Date    ${expected_value}
                ${expected_date_array}=       Split String    ${converted_date}       separator=${SPACE}
                ${expected_value}=      Set Variable        ${expected_date_array}[0]
                 Log Many        expected_value: ${expected_value}
                # Extract column name from the current row's results
                ${curr_row_value}=   Set Variable    ${row[${key}]}
                ${curr_row_value}=   Convert to String      ${curr_row_value}
                ${curr_row_value_array}=   Split String    ${curr_row_value.strip()}       separator=${SPACE}
                ${curr_row_value}=   Set Variable    ${curr_row_value_array}[0]
            ELSE
               # Extract column name from the current row's results
                ${curr_row_value}=   Set Variable    ${row[${key}]}
                ${curr_row_value}=   Convert to String      ${curr_row_value}
            END


            # Check if the row's column matches the expected value
            IF    "${curr_row_value.strip()}" != "${expected_value.strip()}"
                 ${matching_row_found}=   Set Variable    ${False}
            ELSE
                 ${matching_row_found}=   Set Variable    ${True}
            END
            Exit For Loop If    not ${matching_row_found}
        END
        # If the row matches all conditions, add it to the matching rows list
        Run Keyword If    ${matching_row_found}    Append To List    ${matching_rows}    ${row}
        ${matching_rows_len}=   Get Length    ${matching_rows}
        Exit For Loop If    ${matching_row_found}

    END
    # Return the list of matching rows
    RETURN    ${matching_rows}

Get Row By Column Value From DB results and warn if no row found
    [Arguments]  ${db_results}  ${column_name}  ${value_to_find}
    FOR  ${row}  IN  @{db_results}
      ${col_data}=      Set Variable    ${row[${column_name}]}
      Run Keyword If  "${col_data}" == "${value_to_find}"  Return From Keyword  ${row}
    END
    Run Keyword And Warn On Failure     Fail  No row found with ${column_name} = ${value_to_find}
    RETURN  None

Get ATM Details For Comparison
    [Documentation]    Get ATM details from database for frontend comparison using centralized SQL
    [Arguments]    ${atm_id}
    [Return]    Dictionary containing ATM details or empty if not found

    # Use centralized SQL query
    ${query}=    Set Variable    ${SQL_GET_ATM_DETAILS_FOR_COMPARISON}
    ${query}=    Replace String    ${query}    ATM_ID    ${atm_id}

    # Execute query using centralized database connection
    TRY
        ${results}=    Execute VMS Query    ${query}

        # Check if results are empty using length approach
        ${results_length}=    Get Length    ${results}
        IF    ${results_length} == 0
            Log To Console    --------------------------ATM ${atm_id} not found in database
            RETURN    ${EMPTY}
        END

        ${first_row}=    Get From List    ${results}    0
        RETURN    ${first_row}

    EXCEPT    AS    ${error}
        Log To Console    --------------------------Database query failed for ATM ${atm_id}: ${error}
        RETURN    ${EMPTY}
    END

Validate ATM Exists In Database
    [Documentation]    Check if ATM exists in database using centralized SQL
    [Arguments]    ${atm_id}
    [Return]    Boolean indicating if ATM exists

    # Use centralized SQL query
    ${query}=    Set Variable    ${SQL_CHECK_ATM_EXISTS}
    ${query}=    Replace String    ${query}    ATM_ID    ${atm_id}

    # Execute query using centralized database connection
    TRY
        ${results}=    Execute VMS Query    ${query}

        # Check if results are empty using length approach
        ${results_length}=    Get Length    ${results}
        IF    ${results_length} == 0
            RETURN    ${False}
        END

        ${first_row}=    Get From List    ${results}    0
        ${count}=    Get From Dictionary    ${first_row}    atm_count
        ${exists}=    Evaluate    int(${count}) > 0

        RETURN    ${exists}

    EXCEPT    AS    ${error}
        Log To Console    --------------------------Database query failed for ATM ${atm_id}: ${error}
        RETURN    ${False}
    END


