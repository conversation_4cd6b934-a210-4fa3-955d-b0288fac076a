<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.2.2 (Python 3.12.10 on win32)" generated="2025-06-21T10:08:38.913986" rpa="false" schemaversion="5">
<suite id="s1" name="VMS Portal" source="C:\Alternative\Alternative_Physical_Channels_QA\vms\tests\ATM_DETAILS\RAC29a-TC-225_Validate_Search_-_Serial_Number_Column_-_on_ATM_Details.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T10:08:42.854933" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-21T10:08:42.854933" elapsed="0.000000"/>
</kw>
<arg>'${UPLOAD_TEST_STEPS}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${UPLOAD_TEST_STEPS} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T10:08:42.853940" elapsed="0.000993"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T10:08:42.854933" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'Bin_Tables.xml'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-21T10:08:42.854933" elapsed="0.000000"/>
</kw>
<arg>'${ROBOT_FILE_PATH}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${ROBOT_FILE_PATH} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T10:08:42.854933" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T10:08:42.855933" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-21T10:08:42.855933" elapsed="0.000000"/>
</kw>
<arg>'${IS_HEADLESS_BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${IS_HEADLESS_BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T10:08:42.854933" elapsed="0.001000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-21T10:08:42.855933" level="INFO">Environment variable  does not exist.</msg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T10:08:42.855933" elapsed="0.000000"/>
</kw>
<arg>'${BASE_URL}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T10:08:42.855933" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T10:08:42.855933" level="INFO">Environment variable 'BROWSER' set to value 'edge'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-21T10:08:42.855933" elapsed="0.000000"/>
</kw>
<arg>'${BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T10:08:42.855933" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T10:08:42.855933" level="INFO">Environment variable 'SUITE_DIRECTORY' set to value 'vms/data'.</msg>
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-21T10:08:42.855933" elapsed="0.001509"/>
</kw>
<arg>'${SUITE_DIRECTORY}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'SUITE_DIRECTORY', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T10:08:42.855933" elapsed="0.001509"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-21T10:08:42.859603" level="INFO">Environment variable called 'MS_USERNAME', does not exist.</msg>
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T10:08:42.858809" elapsed="0.000794"/>
</kw>
<arg>'${MS_USERNAME}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_USERNAME</arg>
<arg>${MS_USERNAME}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T10:08:42.857988" elapsed="0.001615"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-21T10:08:42.861158" level="INFO">Environment variable called 'MS_PASSWORD', does not exist.</msg>
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T10:08:42.860150" elapsed="0.001008"/>
</kw>
<arg>'${MS_PASSWORD}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_PASSWORD</arg>
<arg>${MS_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T10:08:42.860150" elapsed="0.001008"/>
</kw>
<if>
<branch type="IF" condition="'${TEST_CASE_ID}' != ''">
<kw name="Get Test Case ID" owner="SetEnvironmentVariales">
<var>${TEST_CASE_ID}</var>
<arg>${TEST_CASE_ID}</arg>
<status status="NOT RUN" start="2025-06-21T10:08:42.861158" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Test Case ID: ${TEST_CASE_ID}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-06-21T10:08:42.862154" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T10:08:42.861158" elapsed="0.000996"/>
</branch>
<status status="PASS" start="2025-06-21T10:08:42.861158" elapsed="0.000996"/>
</if>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-21T10:08:42.862154" level="INFO">Environment variable called 'TEST_CASE_ID', does not exist.</msg>
<arg>Environment variable called 'TEST_CASE_ID', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T10:08:42.862154" elapsed="0.000000"/>
</kw>
<arg>'${TEST_CASE_ID}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>TEST_CASE_ID</arg>
<arg>${TEST_CASE_ID}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'TEST_CASE_ID', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T10:08:42.862154" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-21T10:08:42.852942" elapsed="0.009212"/>
</kw>
<test id="s1-t1" name="Validate Search - Serial Number Coloumn- on ATM Details" line="40">
<kw name="Validate Search - Serial Number Coloumn- on ATM Details">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2025-06-21T10:08:42.864670" level="INFO">Set test documentation to:
Validate Search - Serial Number Coloumn- on ATM Details</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2025-06-21T10:08:42.863662" elapsed="0.001008"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-21T10:08:42.864670" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-21T10:08:42.864670" elapsed="0.001001"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T10:08:42.865671" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T10:08:42.865671" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="VMS Windows system Login" owner="Login">
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T10:08:42.865671" level="FAIL">Environment variable 'BASE_URL' does not exist.</msg>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="FAIL" start="2025-06-21T10:08:42.865671" elapsed="0.000000">Environment variable 'BASE_URL' does not exist.</status>
</kw>
<msg time="2025-06-21T10:08:42.865671" level="INFO">${url_exists_on_env_var} = False</msg>
<var>${url_exists_on_env_var}</var>
<arg>Get Environment Variable</arg>
<arg>BASE_URL</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-21T10:08:42.865671" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="${url_exists_on_env_var}">
<kw name="Get Environment Variable" owner="OperatingSystem">
<var>${BASE_URL}</var>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="NOT RUN" start="2025-06-21T10:08:42.865671" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T10:08:42.865671" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T10:08:42.865671" level="INFO">${BASE_URL} = VMS_PROD</msg>
<var>${BASE_URL}</var>
<arg>${URL}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T10:08:42.865671" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-21T10:08:42.865671" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-06-21T10:08:42.865671" elapsed="0.000000"/>
</if>
<kw name="Read Config Property" owner="CommonUtils">
<msg time="2025-06-21T10:08:42.963735" level="INFO">Property value fetched is:  https://vms.absa.africa/</msg>
<msg time="2025-06-21T10:08:42.963735" level="INFO">${base_url} = https://vms.absa.africa/</msg>
<var>${base_url}</var>
<arg>${BASE_URL}</arg>
<status status="PASS" start="2025-06-21T10:08:42.865671" elapsed="0.098064"/>
</kw>
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-21T10:08:42.963735" level="INFO">${PROCESS_NAME_LowerCase} = edge</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-21T10:08:42.963735" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T10:08:42.963735" level="INFO">${PROCESS_NAME_LowerCase} = msedge</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T10:08:42.963735" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-21T10:08:42.963735" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-06-21T10:08:42.963735" elapsed="0.000000"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-06-21T10:08:42.963735" level="INFO">${handle} = msedge.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-06-21T10:08:42.963735" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T10:08:42.963735" elapsed="0.010155"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2025-06-21T10:08:43.233839" level="INFO">Running command 'taskkill /F /IM msedge.exe 2&gt;&amp;1'.</msg>
<msg time="2025-06-21T10:08:44.253613" level="INFO">${rc_code} = 0</msg>
<msg time="2025-06-21T10:08:44.253613" level="INFO">${output} = SUCCESS: The process "msedge.exe" with PID 26984 has been terminated.</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2025-06-21T10:08:42.973890" elapsed="1.279723"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T10:08:44.253613" elapsed="0.000000"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2025-06-21T10:08:42.963735" elapsed="1.289878"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T10:08:44.253613" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2025-06-21T10:08:44.253613" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-06-21T10:08:44.253613" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-06-21T10:08:44.253613" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-06-21T10:08:44.253613" level="INFO">${browser_name} = EDGE</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-06-21T10:08:44.253613" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T10:08:44.253613" level="INFO">${user_home} = C:\Users\<USER>\Users\AB038N8</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T10:08:44.253613" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T10:08:44.258145" level="INFO">${edgedriver_path} = C:/bin/msedgedriver.exe</msg>
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T10:08:44.253613" elapsed="0.004532"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<msg time="2025-06-21T10:08:44.258145" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x0000017B7ED49D00&gt;</msg>
<var>${edge_options}</var>
<status status="PASS" start="2025-06-21T10:08:44.258145" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2025-06-21T10:08:44.258145" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-06-21T10:08:44.258145" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T10:08:44.258145" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-21T10:08:44.258145" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x0000017B7ED4A180&gt;</msg>
<var>${edge_options}</var>
<arg>sys.modules['selenium.webdriver'].EdgeOptions()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-21T10:08:44.258145" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T10:08:44.258145" level="INFO">${root} = C:\Users\<USER>\</msg>
<var>${root}</var>
<arg>${user_home}\\</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T10:08:44.258145" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T10:08:44.258145" level="INFO">${path} = Downloads</msg>
<var>${path}</var>
<arg>Downloads</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T10:08:44.258145" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T10:08:44.258145" level="INFO">${downl_path} = C:\Users\<USER>\Downloads</msg>
<var>${downl_path}</var>
<arg>${root}${path}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T10:08:44.258145" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2025-06-21T10:08:44.258145" level="INFO">${prefs} = {'download.default_directory': 'C:\\Users\\<USER>\\Downloads', 'download.directory_upgrade': 'True', 'savefile.default_directory': 'C:\\Users\\<USER>\\Downloads', 'download.prompt_for_download': 'Fa...</msg>
<var>${prefs}</var>
<arg>download.default_directory=${downl_path}</arg>
<arg>download.directory_upgrade=True</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>savefile.default_directory=${downl_path}</arg>
<arg>download.prompt_for_download=False</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2025-06-21T10:08:44.258145" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${edge_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2025-06-21T10:08:44.258145" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<msg time="2025-06-21T10:08:44.258145" level="WARN">exexcutable_path is being deprecated. Please use service to configure the driver's executable_path as per documentation.</msg>
<msg time="2025-06-21T10:08:44.258145" level="INFO">Opening browser 'edge' to base url 'about:blank'.</msg>
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="PASS" start="2025-06-21T10:08:44.258145" elapsed="3.799625"/>
</kw>
<status status="PASS" start="2025-06-21T10:08:44.258145" elapsed="3.799625"/>
</branch>
<status status="PASS" start="2025-06-21T10:08:44.258145" elapsed="3.799625"/>
</if>
<status status="PASS" start="2025-06-21T10:08:44.253613" elapsed="3.804157"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T10:08:48.057770" elapsed="0.006011"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-21T10:08:48.063781" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T10:08:48.063781" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T10:08:48.063781" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T10:08:48.063781" elapsed="0.000000"/>
</branch>
<status status="NOT RUN" start="2025-06-21T10:08:48.063781" elapsed="0.000000"/>
</if>
<kw name="Set Variable" owner="BuiltIn">
<var>${root}</var>
<arg>${user_home}\\</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T10:08:48.063781" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${path}</var>
<arg>Downloads</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T10:08:48.063781" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${downl_path}</var>
<arg>${root}${path}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T10:08:48.063781" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>download.directory_upgrade=True</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>savefile.default_directory=${downl_path}</arg>
<arg>download.prompt_for_download=False</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="NOT RUN" start="2025-06-21T10:08:48.063781" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T10:08:48.063781" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T10:08:48.063781" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T10:08:48.063781" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T10:08:48.063781" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--incognito</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T10:08:48.063781" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T10:08:48.063781" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-21T10:08:48.063781" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T10:08:48.063781" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${chrome_options}</arg>
<arg>executable_path=${chromedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-06-21T10:08:48.063781" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T10:08:48.057770" elapsed="0.006011"/>
</branch>
<status status="PASS" start="2025-06-21T10:08:44.253613" elapsed="3.810168"/>
</if>
<status status="PASS" start="2025-06-21T10:08:42.963735" elapsed="5.100046"/>
</kw>
<kw name="Load" owner="Login">
<kw name="Maximize Browser Window" owner="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" start="2025-06-21T10:08:48.063781" elapsed="0.049650"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Go To" owner="SeleniumLibrary">
<msg time="2025-06-21T10:08:48.113431" level="INFO">Opening url 'https://vms.absa.africa/'</msg>
<arg>${URL}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<status status="PASS" start="2025-06-21T10:08:48.113431" elapsed="2.805488"/>
</kw>
<arg>'${URL}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${URL}</arg>
<arg>ELSE IF</arg>
<arg>'$${URL}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T10:08:48.113431" elapsed="2.805488"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T10:09:00.925766" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T10:08:50.918919" elapsed="10.006847"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-21T10:09:00.933773" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-21T10:09:01.063527" level="INFO">${element_count_1} = 0</msg>
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-21T10:09:00.933773" elapsed="0.137390"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Navigation.Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-21T10:09:01.071163" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T10:09:11.076017" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T10:09:01.073789" elapsed="10.002228"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-21T10:09:11.076017" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-21T10:09:11.113292" level="INFO">${element_count_2} = 0</msg>
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-21T10:09:11.076017" elapsed="0.037275"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Navigation.Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-21T10:09:11.113292" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T10:09:16.113866" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T10:09:11.113292" elapsed="5.000574"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-21T10:09:16.113866" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-21T10:09:16.144215" level="INFO">${element_count_3} = 0</msg>
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-21T10:09:16.113866" elapsed="0.030349"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-21T10:09:16.153058" elapsed="0.000000"/>
</kw>
<arg>${base_url}</arg>
<status status="PASS" start="2025-06-21T10:08:48.063781" elapsed="28.089277"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-21T10:09:16.273072" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T10:09:16.153058" elapsed="0.120014"/>
</kw>
<msg time="2025-06-21T10:09:16.273072" level="INFO">${User_Name_Element_Visible} = True</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-21T10:09:16.153058" elapsed="0.120014"/>
</kw>
<while condition="${User_Name_Element_Visible}">
<iter>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-21T10:09:16.283154" level="INFO">Typing text 'AB038N8' into text field 'name=txtUsername'.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T10:09:16.283154" elapsed="0.300001"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T10:09:16.583155" elapsed="0.064938"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-21T10:09:16.653166" level="INFO">Typing text '67355870@SitholeBrother' into text field 'name=txtPassword'.</msg>
<arg>${VMS_PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T10:09:16.648093" elapsed="0.385255"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-21T10:09:17.033348" level="INFO">Clicking button 'name=btnLogon'.</msg>
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T10:09:17.033348" elapsed="4.779825"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T10:09:23.813463" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T10:09:21.813173" elapsed="2.000290"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-21T10:09:23.873183" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T10:09:23.813463" elapsed="0.059720"/>
</kw>
<msg time="2025-06-21T10:09:23.873183" level="INFO">${User_Name_Element_Visible} = True</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-21T10:09:23.813463" elapsed="0.059720"/>
</kw>
<status status="PASS" start="2025-06-21T10:09:16.283154" elapsed="7.590029"/>
</iter>
<iter>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-21T10:09:23.883054" level="INFO">Typing text 'AB038N8' into text field 'name=txtUsername'.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T10:09:23.883054" elapsed="0.234391"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T10:09:24.123003" elapsed="0.068600"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-21T10:09:24.193113" level="INFO">Typing text '67355870@SitholeBrother' into text field 'name=txtPassword'.</msg>
<arg>${VMS_PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T10:09:24.193113" elapsed="0.489740"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-21T10:09:24.682853" level="INFO">Clicking button 'name=btnLogon'.</msg>
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T10:09:24.682853" elapsed="3.259977"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T10:09:29.943183" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T10:09:27.942830" elapsed="2.000353"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-21T10:09:30.013060" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T10:09:29.943183" elapsed="0.074497"/>
</kw>
<msg time="2025-06-21T10:09:30.017680" level="INFO">${User_Name_Element_Visible} = True</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-21T10:09:29.943183" elapsed="0.074497"/>
</kw>
<status status="PASS" start="2025-06-21T10:09:23.873183" elapsed="6.144497"/>
</iter>
<iter>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-21T10:09:30.017680" level="INFO">Typing text 'AB038N8' into text field 'name=txtUsername'.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T10:09:30.017680" elapsed="0.260492"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T10:09:30.278172" elapsed="0.076739"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-21T10:09:30.356910" level="INFO">Typing text '67355870@SitholeBrother' into text field 'name=txtPassword'.</msg>
<arg>${VMS_PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T10:09:30.355914" elapsed="0.406852"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-21T10:09:30.762766" level="INFO">Clicking button 'name=btnLogon'.</msg>
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T10:09:30.762766" elapsed="0.229583"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T10:09:32.993445" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T10:09:30.993119" elapsed="2.000326"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-21T10:09:35.182877" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T10:09:32.995571" elapsed="2.187306"/>
</kw>
<msg time="2025-06-21T10:09:35.182877" level="INFO">${User_Name_Element_Visible} = True</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-21T10:09:32.994461" elapsed="2.188416"/>
</kw>
<status status="PASS" start="2025-06-21T10:09:30.017680" elapsed="5.165197"/>
</iter>
<iter>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-21T10:09:35.182877" level="INFO">Typing text 'AB038N8' into text field 'name=txtUsername'.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T10:09:35.182877" elapsed="0.319883"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T10:09:35.502760" elapsed="0.054101"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-21T10:09:35.563082" level="INFO">Typing text '67355870@SitholeBrother' into text field 'name=txtPassword'.</msg>
<arg>${VMS_PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T10:09:35.563082" elapsed="0.363235"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-21T10:09:35.927855" level="INFO">Clicking button 'name=btnLogon'.</msg>
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T10:09:35.927855" elapsed="0.226091"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T10:09:38.155225" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T10:09:36.153946" elapsed="2.001279"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-21T10:09:38.782932" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T10:09:38.155225" elapsed="0.627707"/>
</kw>
<msg time="2025-06-21T10:09:38.782932" level="INFO">${User_Name_Element_Visible} = True</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-21T10:09:38.155225" elapsed="0.636293"/>
</kw>
<status status="PASS" start="2025-06-21T10:09:35.182877" elapsed="3.608641"/>
</iter>
<iter>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-21T10:09:38.792768" level="INFO">Typing text 'AB038N8' into text field 'name=txtUsername'.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T10:09:38.792768" elapsed="0.308622"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T10:09:39.102653" elapsed="0.040262"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-21T10:09:39.142915" level="INFO">Typing text '67355870@SitholeBrother' into text field 'name=txtPassword'.</msg>
<arg>${VMS_PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T10:09:39.142915" elapsed="0.440451"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-21T10:09:39.583366" level="INFO">Clicking button 'name=btnLogon'.</msg>
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T10:09:39.583366" elapsed="0.213795"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T10:09:41.798739" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T10:09:39.798184" elapsed="2.000555"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-21T10:09:42.472577" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T10:09:41.800625" elapsed="0.671952"/>
</kw>
<msg time="2025-06-21T10:09:42.474851" level="INFO">${User_Name_Element_Visible} = True</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-21T10:09:41.800411" elapsed="0.674440"/>
</kw>
<status status="PASS" start="2025-06-21T10:09:38.791518" elapsed="3.683333"/>
</iter>
<iter>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-21T10:09:42.477024" level="INFO">Typing text 'AB038N8' into text field 'name=txtUsername'.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T10:09:42.475861" elapsed="0.209682"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-21T10:09:42.685543" elapsed="0.057199"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-21T10:09:42.744767" level="INFO">Typing text '67355870@SitholeBrother' into text field 'name=txtPassword'.</msg>
<arg>${VMS_PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T10:09:42.743764" elapsed="0.309006"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-21T10:09:43.060877" level="INFO">Clicking button 'name=btnLogon'.</msg>
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-06-21T10:09:43.058785" elapsed="2.446355"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-21T10:09:47.506707" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-21T10:09:45.506138" elapsed="2.000569"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-21T10:09:51.928402" level="WARN">Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x0000017B7EDA12B0&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9589e94e163fee8c1f18e67255b03414/screenshot</msg>
<msg time="2025-06-21T10:09:56.207369" level="WARN">Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x0000017B7EDA1460&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9589e94e163fee8c1f18e67255b03414/screenshot</msg>
<msg time="2025-06-21T10:10:00.280948" level="WARN">Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x0000017B7EDA1640&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9589e94e163fee8c1f18e67255b03414/screenshot</msg>
<msg time="2025-06-21T10:10:04.608701" level="WARN">Keyword 'Capture Page Screenshot' could not be run on failure: HTTPConnectionPool(host='localhost', port=60148): Max retries exceeded with url: /session/9589e94e163fee8c1f18e67255b03414/screenshot (Caused by NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x0000017B6954D340&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="FAIL" start="2025-06-21T10:09:47.506707" elapsed="17.102914">Execution terminated by signal</status>
</kw>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="FAIL" start="2025-06-21T10:09:47.506707" elapsed="17.102914">Execution terminated by signal</status>
</kw>
<status status="FAIL" start="2025-06-21T10:09:42.474851" elapsed="22.134770">Execution terminated by signal</status>
</iter>
<status status="FAIL" start="2025-06-21T10:09:16.283154" elapsed="48.326467">Execution terminated by signal</status>
</while>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------------ User successfully accessed VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" start="2025-06-21T10:10:04.609621" elapsed="0.000000"/>
</kw>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" start="2025-06-21T10:10:04.609621" elapsed="0.000000"/>
</kw>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<status status="FAIL" start="2025-06-21T10:08:42.865671" elapsed="81.743950">Execution terminated by signal</status>
</kw>
<arg>'${system}' == 'Windows'</arg>
<arg>VMS Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="FAIL" start="2025-06-21T10:08:42.865671" elapsed="81.746968">Execution terminated by signal</status>
</kw>
<arg>${TEST_ENVIRONMENT}</arg>
<status status="FAIL" start="2025-06-21T10:08:42.864670" elapsed="81.748109">Execution terminated by signal</status>
</kw>
<kw name="When The user clicks on the ATM Details link" owner="ATMDetails">
<status status="NOT RUN" start="2025-06-21T10:10:04.612779" elapsed="0.000000"/>
</kw>
<kw name="And The user lands on the ATM Details pages" owner="ATMDetails">
<status status="NOT RUN" start="2025-06-21T10:10:04.612779" elapsed="0.000000"/>
</kw>
<kw name="Then The user searches FrontEnd for Existing ATM" owner="ATMDetails">
<arg>${SEARCH_KEY}</arg>
<status status="NOT RUN" start="2025-06-21T10:10:04.612779" elapsed="0.000000"/>
</kw>
<kw name="Then The user verifies only one row is data present" owner="ATMDetails">
<status status="NOT RUN" start="2025-06-21T10:10:04.612779" elapsed="0.000000"/>
</kw>
<kw name="Then The user verifies that searched key appears in the the correct Column" owner="ATMDetails">
<arg>${COLUMN}</arg>
<arg>${SEARCH_KEY}</arg>
<status status="NOT RUN" start="2025-06-21T10:10:04.612779" elapsed="0.000000"/>
</kw>
<arg>Validate Search - Serial Number Coloumn- on ATM Details</arg>
<arg>VMS_PROD</arg>
<arg>43549618</arg>
<arg>Serial Number</arg>
<status status="FAIL" start="2025-06-21T10:08:42.863662" elapsed="81.749117">Execution terminated by signal</status>
</kw>
<kw name="The user logs out of VMS" owner="Logout" type="TEARDOWN">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<msg time="2025-06-21T10:10:08.758159" level="WARN">Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x0000017B7EDA0590&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9589e94e163fee8c1f18e67255b03414/elements</msg>
<msg time="2025-06-21T10:10:13.163339" level="WARN">Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x0000017B7ED76660&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9589e94e163fee8c1f18e67255b03414/elements</msg>
<msg time="2025-06-21T10:10:17.216207" level="WARN">Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x0000017B7ED76A50&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9589e94e163fee8c1f18e67255b03414/elements</msg>
<msg time="2025-06-21T10:10:25.341948" level="WARN">Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x0000017B7ED4CD10&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9589e94e163fee8c1f18e67255b03414/screenshot</msg>
<msg time="2025-06-21T10:10:29.423289" level="WARN">Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x0000017B7ED49490&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9589e94e163fee8c1f18e67255b03414/screenshot</msg>
<msg time="2025-06-21T10:10:33.494901" level="WARN">Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x0000017B7ED49DC0&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9589e94e163fee8c1f18e67255b03414/screenshot</msg>
<msg time="2025-06-21T10:10:37.591059" level="WARN">Keyword 'Capture Page Screenshot' could not be run on failure: HTTPConnectionPool(host='localhost', port=60148): Max retries exceeded with url: /session/9589e94e163fee8c1f18e67255b03414/screenshot (Caused by NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x0000017B7ED48F50&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))</msg>
<msg time="2025-06-21T10:10:37.592139" level="FAIL">MaxRetryError: HTTPConnectionPool(host='localhost', port=60148): Max retries exceeded with url: /session/9589e94e163fee8c1f18e67255b03414/elements (Caused by NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x0000017B7ED4CFE0&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))</msg>
<arg>${VMS_LOGOUT_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="FAIL" start="2025-06-21T10:10:04.617839" elapsed="33.614195">MaxRetryError: HTTPConnectionPool(host='localhost', port=60148): Max retries exceeded with url: /session/9589e94e163fee8c1f18e67255b03414/elements (Caused by NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x0000017B7ED4CFE0&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))</status>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------------------ Logging out of VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T10:10:38.232034" elapsed="0.000000"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-21T10:10:38.232034" level="INFO">Clicking button 'name=ctl00$btnLogout'.</msg>
<msg time="2025-06-21T10:10:42.292829" level="WARN">Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x0000017B7ED74D70&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9589e94e163fee8c1f18e67255b03414/elements</msg>
<msg time="2025-06-21T10:10:46.706769" level="WARN">Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x0000017B7ED75580&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9589e94e163fee8c1f18e67255b03414/elements</msg>
<errors>
<msg time="2025-06-21T10:08:42.021043" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 312: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T10:08:42.021043" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 342: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T10:08:42.023555" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 373: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T10:08:42.023555" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 413: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T10:08:42.023555" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 458: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T10:08:42.023555" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 470: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T10:08:42.023555" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 523: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T10:08:42.023555" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 816: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T10:08:42.023555" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 847: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T10:08:42.023555" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 878: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T10:08:42.023555" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 914: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T10:08:42.023555" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 924: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T10:08:42.023555" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 935: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T10:08:42.023555" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 953: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T10:08:42.029039" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 978: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T10:08:42.029424" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 998: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T10:08:42.029424" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 1025: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T10:08:42.030509" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 1056: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T10:08:42.031544" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 1162: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T10:08:42.032547" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 1417: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T10:08:42.033943" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 1528: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T10:08:42.053456" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\common_keywords.robot' on line 117: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T10:08:42.053456" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\common_keywords.robot' on line 137: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T10:08:42.175021" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\DatabaseConnector.robot' on line 355: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T10:08:42.175021" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\DatabaseConnector.robot' on line 389: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T10:08:42.837088" level="ERROR">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\SetEnvironmentVariales.robot' on line 14: Importing library 'C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py' failed: ModuleNotFoundError: No module named 'acintegration'
Traceback (most recent call last):
  File "C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py", line 1, in &lt;module&gt;
    from acintegration.QMetryIntegration import QMetryIntegration
PYTHONPATH:
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\robot.exe
  C:\Alternative
  C:\Alternative\Alternative_Physical_Channels_QA
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages</msg>
<msg time="2025-06-21T10:08:42.843737" level="WARN">Imported library 'C:\Alternative\Alternative_Physical_Channels_QA\future_fit_architecture_portal_BinTables\keywords\controllers\resources\RestRequestsUnMarshal.py' contains no keywords.</msg>
<msg time="2025-06-21T10:08:44.258145" level="WARN">exexcutable_path is being deprecated. Please use service to configure the driver's executable_path as per documentation.</msg>
<msg time="2025-06-21T10:09:00.933773" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-21T10:09:11.076017" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-21T10:09:16.113866" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-21T10:09:51.928402" level="WARN">Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x0000017B7EDA12B0&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9589e94e163fee8c1f18e67255b03414/screenshot</msg>
<msg time="2025-06-21T10:09:56.207369" level="WARN">Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x0000017B7EDA1460&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9589e94e163fee8c1f18e67255b03414/screenshot</msg>
<msg time="2025-06-21T10:10:00.280948" level="WARN">Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x0000017B7EDA1640&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9589e94e163fee8c1f18e67255b03414/screenshot</msg>
<msg time="2025-06-21T10:10:04.608701" level="WARN">Keyword 'Capture Page Screenshot' could not be run on failure: HTTPConnectionPool(host='localhost', port=60148): Max retries exceeded with url: /session/9589e94e163fee8c1f18e67255b03414/screenshot (Caused by NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x0000017B6954D340&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))</msg>
<msg time="2025-06-21T10:10:08.758159" level="WARN">Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x0000017B7EDA0590&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9589e94e163fee8c1f18e67255b03414/elements</msg>
<msg time="2025-06-21T10:10:13.163339" level="WARN">Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x0000017B7ED76660&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9589e94e163fee8c1f18e67255b03414/elements</msg>
<msg time="2025-06-21T10:10:17.216207" level="WARN">Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x0000017B7ED76A50&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9589e94e163fee8c1f18e67255b03414/elements</msg>
<msg time="2025-06-21T10:10:25.341948" level="WARN">Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x0000017B7ED4CD10&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9589e94e163fee8c1f18e67255b03414/screenshot</msg>
<msg time="2025-06-21T10:10:29.423289" level="WARN">Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x0000017B7ED49490&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9589e94e163fee8c1f18e67255b03414/screenshot</msg>
<msg time="2025-06-21T10:10:33.494901" level="WARN">Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x0000017B7ED49DC0&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9589e94e163fee8c1f18e67255b03414/screenshot</msg>
<msg time="2025-06-21T10:10:37.591059" level="WARN">Keyword 'Capture Page Screenshot' could not be run on failure: HTTPConnectionPool(host='localhost', port=60148): Max retries exceeded with url: /session/9589e94e163fee8c1f18e67255b03414/screenshot (Caused by NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x0000017B7ED48F50&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))</msg>
<msg time="2025-06-21T10:10:42.292829" level="WARN">Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x0000017B7ED74D70&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9589e94e163fee8c1f18e67255b03414/elements</msg>
<msg time="2025-06-21T10:10:46.706769" level="WARN">Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x0000017B7ED75580&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9589e94e163fee8c1f18e67255b03414/elements</msg>
<msg time="2025-06-21T10:10:51.257967" level="ERROR">Execution stopped by user.</msg>
</errors>
</robot>
