server-api: 2025-07-03 09:37:25 UTC pid: 2532 - MainThread - INFO - robotframework_ls.__main__
Arguments: ['-v', '--log-file=c:\\Alternative\\robot_lsp.log.5.lint.api']

server-api: 2025-07-03 09:37:25 UTC pid: 2532 - MainThread - INFO - robotframework_ls.__main__
Python: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)] - lsp: 1.13.0 (<module 'robotframework_ls' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\robotframework_ls\\__init__.py'>) - platform: win32 - sys.prefix: C:\Users\<USER>\AppData\Local\Programs\Python\Python312 - sys.executable: C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe

server-api: 2025-07-03 09:37:25 UTC pid: 2532 - MainThread - INFO - robotframework_ls.__main__
CPUs: 12

server-api: 2025-07-03 09:37:25 UTC pid: 2532 - MainThread - INFO - robocorp_ls_core.python_ls
Starting RobotFrameworkServerApiWithObserver IO language server. pid: 2532

server-api: 2025-07-03 09:37:29 UTC pid: 2532 - MainThread - INFO - robotframework_ls.impl.libspec_manager
User libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\user

server-api: 2025-07-03 09:37:29 UTC pid: 2532 - MainThread - INFO - robotframework_ls.impl.libspec_manager
Builtins libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\builtins

server-api: 2025-07-03 09:37:29 UTC pid: 2532 - MainThread - INFO - robotframework_ls.impl.libspec_manager
Cache libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\cache

server-api: 2025-07-03 09:37:36 UTC pid: 2532 - ThreadPoolExecutor-0_0 - EXCEPTION - robotframework_ls.server_api.server
Error collecting Robocop errors (possibly an unsupported Robocop version is installed).

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\vendored\robocorp_ls_core\robocop_wrapper.py", line 16, in _import_robocop
    import robocop
ModuleNotFoundError: No module named 'robocop'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\server_api\server.py", line 475, in _threaded_lint
    collect_robocop_diagnostics(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\vendored\robocorp_ls_core\robocop_wrapper.py", line 33, in collect_robocop_diagnostics
    _import_robocop()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\vendored\robocorp_ls_core\robocop_wrapper.py", line 23, in _import_robocop
    import robocop  # @UnusedImport
    ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\vendored\robocorp_ls_core\libs\robocop_lib\robocop\__init__.py", line 1, in <module>
    from robocop import checkers
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\vendored\robocorp_ls_core\libs\robocop_lib\robocop\checkers\__init__.py", line 42, in <module>
    from robocop.utils import modules_from_paths, modules_in_current_dir
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\vendored\robocorp_ls_core\libs\robocop_lib\robocop\utils\__init__.py", line 5, in <module>
    from robocop.utils.file_types import FileType, FileTypeChecker
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\vendored\robocorp_ls_core\libs\robocop_lib\robocop\utils\file_types.py", line 12, in <module>
    from robocop.utils.misc import rf_supports_lang
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\vendored\robocorp_ls_core\libs\robocop_lib\robocop\utils\misc.py", line 17, in <module>
    from robot.variables.search import VariableIterator
ImportError: cannot import name 'VariableIterator' from 'robot.variables.search' (C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robot\variables\search.py)
server-api: 2025-07-03 09:37:42 UTC pid: 2532 - Thread-5 - CRITICAL - robocorp_ls_core.jsonrpc.endpoint
===============================================================================
Slow request (already took: 8s). Showing thread dump.
================================= Thread Dump =================================

-------------------------------------------------------------------------------
 Thread <Thread(ThreadPoolExecutor-0_1, started 30424)>

self: <Thread(Thread-11 (_readerthread), started daemon 25040)>

 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1032, in _bootstrap
   self._bootstrap_inner()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1075, in _bootstrap_inner
   self.run()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1012, in run
   self._target(*self._args, **self._kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\concurrent\futures\thread.py", line 93, in _worker
   work_item.run()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\concurrent\futures\thread.py", line 59, in run
   result = self.fn(*self.args, **self.kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\endpoint.py", line 265, in _call_checking_time
   return func(**kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\server_api\server.py", line 446, in _threaded_lint
   analysis_errors = code_analysis.collect_analysis_errors(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\code_analysis.py", line 390, in collect_analysis_errors
   collect_keywords(initial_completion_context, collector)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\collect_keywords.py", line 583, in collect_keywords
   _collect_from_context(completion_context, collector)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\collect_keywords.py", line 479, in _collect_from_context
   _collect_libraries_keywords(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\collect_keywords.py", line 357, in _collect_libraries_keywords
   libspec_manager.get_library_doc_or_error(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1366, in get_library_doc_or_error
   error_msg = self._do_create_libspec_on_get(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1163, in _do_create_libspec_on_get
   error_creating = self._create_libspec(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 918, in _create_libspec
   error_creating = self._cached_create_libspec(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1051, in _cached_create_libspec
   self._subprocess_check_output(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 933, in _subprocess_check_output
   return subprocess.check_output(*args, **kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 466, in check_output
   return run(*popenargs, stdout=PIPE, timeout=timeout, check=True,
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 550, in run
   stdout, stderr = process.communicate(input, timeout=timeout)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 1209, in communicate
   stdout, stderr = self._communicate(input, endtime, timeout)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 1628, in _communicate
   self.stdout_thread.join(self._remaining_time(endtime))
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1149, in join
   self._wait_for_tstate_lock()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1169, in _wait_for_tstate_lock
   if lock.acquire(block, timeout):

=============================== END Thread Dump ===============================

server-api: 2025-07-03 09:37:57 UTC pid: 2532 - Thread-3 - EXCEPTION - robotframework_ls.impl.libspec_markdown_conversion
Error converting libspec to markdown: c:\users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\user\b1de733b.libspec.
Return code: 1
Output:
Traceback (most recent call last):

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_markdown_conversion.py", line 228, in <module>

    _convert_to_markdown_if_needed(spec_filename, target_json)

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_markdown_conversion.py", line 97, in _convert_to_markdown_if_needed

    mtime = os.path.getmtime(spec_filename)

            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

  File "<frozen genericpath>", line 67, in getmtime

FileNotFoundError: [WinError 3] The system cannot find the path specified: 'c:\\users\\<USER>\\.robotframework-ls\\specs\\v2\\5c98395c_7.2.2\\user\\b1de733b.libspec'



Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_markdown_conversion.py", line 159, in run
    subprocess.check_output(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 466, in check_output
    return run(*popenargs, stdout=PIPE, timeout=timeout, check=True,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 571, in run
    raise CalledProcessError(retcode, process.args,
subprocess.CalledProcessError: Command '['C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python.exe', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\robotframework_ls\\impl\\libspec_markdown_conversion.py', 'c:\\users\\<USER>\\.robotframework-ls\\specs\\v2\\5c98395c_7.2.2\\user\\b1de733b.libspec', 'C:\\Users\\<USER>\\.robotframework-ls\\specs\\v2\\5c98395c_7.2.2\\cache\\b5a55b3e_b1de733b.libspec.json']' returned non-zero exit status 1.
