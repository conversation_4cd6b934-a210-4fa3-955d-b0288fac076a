<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.2.2 (Python 3.12.10 on win32)" generated="2025-06-30T15:29:19.909081" rpa="false" schemaversion="5">
<suite id="s1" name="VMS Portal" source="C:\Alternative\Alternative_Physical_Channels_QA\vms\tests\SITE_MAINTENANCE\RAC29a_TC_241_Page_Navigation_Validation_Prev_Next_on_site_maintenance.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-30T15:29:27.348321" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-30T15:29:27.347298" elapsed="0.001023"/>
</kw>
<arg>'${UPLOAD_TEST_STEPS}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${UPLOAD_TEST_STEPS} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-30T15:29:27.347298" elapsed="0.001023"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-30T15:29:27.349295" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'Bin_Tables.xml'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-30T15:29:27.348321" elapsed="0.000974"/>
</kw>
<arg>'${ROBOT_FILE_PATH}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${ROBOT_FILE_PATH} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-30T15:29:27.348321" elapsed="0.000974"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-30T15:29:27.349295" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-30T15:29:27.349295" elapsed="0.000998"/>
</kw>
<arg>'${IS_HEADLESS_BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${IS_HEADLESS_BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-30T15:29:27.349295" elapsed="0.000998"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-30T15:29:27.350293" level="INFO">Environment variable  does not exist.</msg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-30T15:29:27.350293" elapsed="0.000000"/>
</kw>
<arg>'${BASE_URL}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-30T15:29:27.350293" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-30T15:29:27.351292" level="INFO">Environment variable 'BROWSER' set to value 'edge'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-30T15:29:27.351292" elapsed="0.000000"/>
</kw>
<arg>'${BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-30T15:29:27.351292" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-30T15:29:27.352311" level="INFO">Environment variable 'SUITE_DIRECTORY' set to value 'vms/data'.</msg>
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-30T15:29:27.352311" elapsed="0.000000"/>
</kw>
<arg>'${SUITE_DIRECTORY}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'SUITE_DIRECTORY', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-30T15:29:27.351292" elapsed="0.001019"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-30T15:29:27.353317" level="INFO">Environment variable called 'MS_USERNAME', does not exist.</msg>
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-30T15:29:27.352311" elapsed="0.001006"/>
</kw>
<arg>'${MS_USERNAME}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_USERNAME</arg>
<arg>${MS_USERNAME}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-30T15:29:27.352311" elapsed="0.001006"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-30T15:29:27.353864" level="INFO">Environment variable called 'MS_PASSWORD', does not exist.</msg>
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-30T15:29:27.353864" elapsed="0.000000"/>
</kw>
<arg>'${MS_PASSWORD}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_PASSWORD</arg>
<arg>${MS_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-30T15:29:27.353317" elapsed="0.000547"/>
</kw>
<if>
<branch type="IF" condition="'${TEST_CASE_ID}' != ''">
<kw name="Get Test Case ID" owner="SetEnvironmentVariales">
<var>${TEST_CASE_ID}</var>
<arg>${TEST_CASE_ID}</arg>
<status status="NOT RUN" start="2025-06-30T15:29:27.354903" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Test Case ID: ${TEST_CASE_ID}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-06-30T15:29:27.354903" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-30T15:29:27.353864" elapsed="0.001039"/>
</branch>
<status status="PASS" start="2025-06-30T15:29:27.353864" elapsed="0.001039"/>
</if>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-30T15:29:27.355930" level="INFO">Environment variable called 'TEST_CASE_ID', does not exist.</msg>
<arg>Environment variable called 'TEST_CASE_ID', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-30T15:29:27.355930" elapsed="0.000000"/>
</kw>
<arg>'${TEST_CASE_ID}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>TEST_CASE_ID</arg>
<arg>${TEST_CASE_ID}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'TEST_CASE_ID', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-30T15:29:27.354903" elapsed="0.001027"/>
</kw>
<status status="PASS" start="2025-06-30T15:29:27.344267" elapsed="0.012663"/>
</kw>
<test id="s1-t1" name="Page Navigation Validation (Prev &amp; Next)- on site maintenance" line="40">
<kw name="PREV and NEXT Buttons on Site Maintenance">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2025-06-30T15:29:27.360150" level="INFO">Set test documentation to:
Validating the PREV and NEXT Buttons</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2025-06-30T15:29:27.360150" elapsed="0.000000"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-30T15:29:27.361150" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-30T15:29:27.361150" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-30T15:29:27.361150" elapsed="0.001002"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-30T15:29:27.362152" elapsed="0.000996"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="VMS Windows system Login" owner="Login">
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-30T15:29:27.365702" level="FAIL">Environment variable 'BASE_URL' does not exist.</msg>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="FAIL" start="2025-06-30T15:29:27.364687" elapsed="0.001015">Environment variable 'BASE_URL' does not exist.</status>
</kw>
<msg time="2025-06-30T15:29:27.365702" level="INFO">${url_exists_on_env_var} = False</msg>
<var>${url_exists_on_env_var}</var>
<arg>Get Environment Variable</arg>
<arg>BASE_URL</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-30T15:29:27.364687" elapsed="0.001015"/>
</kw>
<if>
<branch type="IF" condition="${url_exists_on_env_var}">
<kw name="Get Environment Variable" owner="OperatingSystem">
<var>${BASE_URL}</var>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="NOT RUN" start="2025-06-30T15:29:27.366702" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-30T15:29:27.365702" elapsed="0.001000"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-30T15:29:27.366702" level="INFO">${BASE_URL} = VMS_UAT</msg>
<var>${BASE_URL}</var>
<arg>${URL}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-30T15:29:27.366702" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-30T15:29:27.366702" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-06-30T15:29:27.365702" elapsed="0.001000"/>
</if>
<kw name="Read Config Property" owner="CommonUtils">
<msg time="2025-06-30T15:29:27.384578" level="INFO">Property value fetched is:  https://vms.uat.absa.africa/</msg>
<msg time="2025-06-30T15:29:27.384797" level="INFO">${base_url} = https://vms.uat.absa.africa/</msg>
<var>${base_url}</var>
<arg>${BASE_URL}</arg>
<status status="PASS" start="2025-06-30T15:29:27.367702" elapsed="0.017095"/>
</kw>
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-30T15:29:27.386813" level="INFO">${PROCESS_NAME_LowerCase} = edge</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-30T15:29:27.386813" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-30T15:29:27.388335" level="INFO">${PROCESS_NAME_LowerCase} = msedge</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-30T15:29:27.387817" elapsed="0.000518"/>
</kw>
<status status="PASS" start="2025-06-30T15:29:27.386813" elapsed="0.001522"/>
</branch>
<status status="PASS" start="2025-06-30T15:29:27.386813" elapsed="0.001522"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-06-30T15:29:27.388335" level="INFO">${handle} = msedge.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-06-30T15:29:27.388335" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-30T15:29:27.389359" elapsed="0.000000"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2025-06-30T15:29:27.706256" level="INFO">Running command 'taskkill /F /IM msedge.exe 2&gt;&amp;1'.</msg>
<msg time="2025-06-30T15:29:28.525870" level="INFO">${rc_code} = 128</msg>
<msg time="2025-06-30T15:29:28.525870" level="INFO">${output} = ERROR: The process "msedge.exe" not found.</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2025-06-30T15:29:27.389359" elapsed="1.136511"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-30T15:29:28.526851" level="WARN">There was error during termination of process</msg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-30T15:29:28.526851" elapsed="0.001014"/>
</kw>
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-30T15:29:28.526851" elapsed="0.001014"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2025-06-30T15:29:27.385819" elapsed="1.142046"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-30T15:29:28.527865" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2025-06-30T15:29:28.527865" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-06-30T15:29:28.528869" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-06-30T15:29:28.528869" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-06-30T15:29:28.528869" level="INFO">${browser_name} = EDGE</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-06-30T15:29:28.528869" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-30T15:29:28.529869" level="INFO">${user_home} = C:\Users\<USER>\Users\AB038N8</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-30T15:29:28.529869" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-30T15:29:28.529869" level="INFO">${edgedriver_path} = C:/bin/msedgedriver.exe</msg>
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-30T15:29:28.529869" elapsed="0.000000"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<msg time="2025-06-30T15:29:28.529869" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x000001D9BB592420&gt;</msg>
<var>${edge_options}</var>
<status status="PASS" start="2025-06-30T15:29:28.529869" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2025-06-30T15:29:28.530867" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-06-30T15:29:28.530867" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-30T15:29:28.530867" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-30T15:29:28.530867" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x000001D9BB5B0B00&gt;</msg>
<var>${edge_options}</var>
<arg>sys.modules['selenium.webdriver'].EdgeOptions()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-30T15:29:28.530867" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-30T15:29:28.530867" level="INFO">${root} = C:\Users\<USER>\</msg>
<var>${root}</var>
<arg>${user_home}\\</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-30T15:29:28.530867" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-30T15:29:28.531868" level="INFO">${path} = Downloads</msg>
<var>${path}</var>
<arg>Downloads</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-30T15:29:28.531868" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-30T15:29:28.531868" level="INFO">${downl_path} = C:\Users\<USER>\Downloads</msg>
<var>${downl_path}</var>
<arg>${root}${path}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-30T15:29:28.531868" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2025-06-30T15:29:28.531868" level="INFO">${prefs} = {'download.default_directory': 'C:\\Users\\<USER>\\Downloads', 'download.directory_upgrade': 'True', 'savefile.default_directory': 'C:\\Users\\<USER>\\Downloads', 'download.prompt_for_download': 'Fa...</msg>
<var>${prefs}</var>
<arg>download.default_directory=${downl_path}</arg>
<arg>download.directory_upgrade=True</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>savefile.default_directory=${downl_path}</arg>
<arg>download.prompt_for_download=False</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2025-06-30T15:29:28.531868" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${edge_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2025-06-30T15:29:28.531868" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<msg time="2025-06-30T15:29:28.531868" level="WARN">exexcutable_path is being deprecated. Please use service to configure the driver's executable_path as per documentation.</msg>
<msg time="2025-06-30T15:29:28.533949" level="INFO">Opening browser 'edge' to base url 'about:blank'.</msg>
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="PASS" start="2025-06-30T15:29:28.531868" elapsed="5.043475"/>
</kw>
<status status="PASS" start="2025-06-30T15:29:28.530867" elapsed="5.044476"/>
</branch>
<status status="PASS" start="2025-06-30T15:29:28.530867" elapsed="5.044476"/>
</if>
<status status="PASS" start="2025-06-30T15:29:28.529869" elapsed="5.045474"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-30T15:29:33.576341" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-30T15:29:33.576341" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-30T15:29:33.577338" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-30T15:29:33.577338" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-30T15:29:33.576341" elapsed="0.001998"/>
</branch>
<status status="NOT RUN" start="2025-06-30T15:29:33.576341" elapsed="0.001998"/>
</if>
<kw name="Set Variable" owner="BuiltIn">
<var>${root}</var>
<arg>${user_home}\\</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-30T15:29:33.578339" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${path}</var>
<arg>Downloads</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-30T15:29:33.578339" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${downl_path}</var>
<arg>${root}${path}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-30T15:29:33.579339" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>download.directory_upgrade=True</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>savefile.default_directory=${downl_path}</arg>
<arg>download.prompt_for_download=False</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="NOT RUN" start="2025-06-30T15:29:33.579339" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-30T15:29:33.580339" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-30T15:29:33.580339" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-30T15:29:33.581337" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-30T15:29:33.581337" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--incognito</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-30T15:29:33.582491" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-30T15:29:33.583014" elapsed="0.000128"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-30T15:29:33.583142" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-30T15:29:33.583142" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${chrome_options}</arg>
<arg>executable_path=${chromedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-06-30T15:29:33.584175" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-30T15:29:33.575343" elapsed="0.008832"/>
</branch>
<status status="PASS" start="2025-06-30T15:29:28.529869" elapsed="5.054306"/>
</if>
<status status="PASS" start="2025-06-30T15:29:27.384797" elapsed="6.199378"/>
</kw>
<kw name="Load" owner="Login">
<kw name="Maximize Browser Window" owner="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" start="2025-06-30T15:29:33.585172" elapsed="0.057794"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Go To" owner="SeleniumLibrary">
<msg time="2025-06-30T15:29:33.650940" level="INFO">Opening url 'https://vms.uat.absa.africa/'</msg>
<arg>${URL}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<status status="PASS" start="2025-06-30T15:29:33.650940" elapsed="3.277535"/>
</kw>
<arg>'${URL}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${URL}</arg>
<arg>ELSE IF</arg>
<arg>'$${URL}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-30T15:29:33.643918" elapsed="3.284557"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-30T15:29:46.929590" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-30T15:29:36.928475" elapsed="10.001115"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-30T15:29:46.929590" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-30T15:29:46.964128" level="INFO">${element_count_1} = 0</msg>
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-30T15:29:46.929590" elapsed="0.034538"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Navigation.Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-30T15:29:46.964128" elapsed="0.001023"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-30T15:29:56.965172" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-30T15:29:46.965151" elapsed="10.000021"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-30T15:29:56.965172" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-30T15:29:56.997357" level="INFO">${element_count_2} = 0</msg>
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-30T15:29:56.965172" elapsed="0.032185"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Navigation.Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-30T15:29:56.997357" elapsed="0.001024"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-30T15:30:01.999773" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-30T15:29:56.999380" elapsed="5.000393"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-30T15:30:01.999773" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-30T15:30:02.023310" level="INFO">${element_count_3} = 0</msg>
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-30T15:30:01.999773" elapsed="0.023537"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-30T15:30:02.023310" elapsed="0.000000"/>
</kw>
<arg>${base_url}</arg>
<status status="PASS" start="2025-06-30T15:29:33.584175" elapsed="28.439135"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-30T15:30:02.214854" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-30T15:30:02.025364" elapsed="0.189490"/>
</kw>
<msg time="2025-06-30T15:30:02.214854" level="INFO">${User_Name_Element_Visible} = True</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-30T15:30:02.023310" elapsed="0.191544"/>
</kw>
<while condition="${User_Name_Element_Visible}">
<iter>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-30T15:30:02.215843" level="INFO">Typing text 'AB038N8' into text field 'name=txtUsername'.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-30T15:30:02.214854" elapsed="0.381173"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-30T15:30:02.597023" elapsed="0.058440"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-30T15:30:02.656490" level="INFO">Typing text '67355870@SitholeBrother' into text field 'name=txtPassword'.</msg>
<arg>${VMS_PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-30T15:30:02.656490" elapsed="0.207005"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-30T15:30:02.864014" level="INFO">Clicking button 'name=btnLogon'.</msg>
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-06-30T15:30:02.864014" elapsed="16.618558"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-30T15:30:21.484180" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-30T15:30:19.483609" elapsed="2.000571"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-30T15:30:21.705773" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-1.png"&gt;&lt;img src="selenium-screenshot-1.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2025-06-30T15:30:21.706771" level="FAIL">Element with locator 'name=txtUsername' not found.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="FAIL" start="2025-06-30T15:30:21.484180" elapsed="1.249441">Element with locator 'name=txtUsername' not found.</status>
</kw>
<msg time="2025-06-30T15:30:22.733621" level="INFO">${User_Name_Element_Visible} = False</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-30T15:30:21.484180" elapsed="1.249441"/>
</kw>
<status status="PASS" start="2025-06-30T15:30:02.214854" elapsed="20.518767"/>
</iter>
<status status="PASS" start="2025-06-30T15:30:02.214854" elapsed="20.518767"/>
</while>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------------ User successfully accessed VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-30T15:30:22.734621" elapsed="0.000000"/>
</kw>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<msg time="2025-06-30T15:30:22.912285" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-2.png"&gt;&lt;img src="selenium-screenshot-2.png" width="800px"&gt;&lt;/a&gt;</msg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="PASS" start="2025-06-30T15:30:22.734621" elapsed="0.177664"/>
</kw>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<status status="PASS" start="2025-06-30T15:29:27.363665" elapsed="55.548620"/>
</kw>
<arg>'${system}' == 'Windows'</arg>
<arg>VMS Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-30T15:29:27.363148" elapsed="55.549137"/>
</kw>
<arg>${TEST_ENVIRONMENT}</arg>
<status status="PASS" start="2025-06-30T15:29:27.360150" elapsed="55.553135"/>
</kw>
<kw name="When The user lands on the dashboard page" owner="Dashboard">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-30T15:30:22.940228" level="INFO">Current page contains text 'Dashboard'.</msg>
<arg>Dashboard</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-30T15:30:22.914285" elapsed="0.025943"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-30T15:30:22.962370" level="INFO">Current page contains text 'Top 10 ATMs with the highest calls'.</msg>
<arg>Top 10 ATMs with the highest calls</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-30T15:30:22.941222" elapsed="0.022152"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-30T15:30:22.988810" level="INFO">Current page contains text 'Main Calls logged for ATMs accross the country'.</msg>
<arg>Main Calls logged for ATMs accross the country</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-30T15:30:22.963374" elapsed="0.025436"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-30T15:30:23.037012" level="INFO">Current page contains text 'Calls logged against Devices'.</msg>
<arg>Calls logged against Devices</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-30T15:30:22.988810" elapsed="0.049289"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-30T15:30:23.066031" level="INFO">Current page contains text 'Top 10 ATMs with the highest calls'.</msg>
<arg>Top 10 ATMs with the highest calls</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-30T15:30:23.038099" elapsed="0.028967"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-30T15:30:23.100335" level="INFO">Current page contains text 'SLA Status per Main Vendor'.</msg>
<arg>SLA Status per Main Vendor</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-30T15:30:23.068076" elapsed="0.032259"/>
</kw>
<status status="PASS" start="2025-06-30T15:30:22.913285" elapsed="0.187050"/>
</kw>
<kw name="And The user navigates to the Site Maintenance Page" owner="Site_Maintenance">
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user clicks navigate to the Site Maintenance Page</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-30T15:30:23.101333" elapsed="0.000000"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-06-30T15:30:23.102328" level="INFO">Clicking element '//*[text()[normalize-space(.)='Site Maintanance']]'.</msg>
<arg>${Site_Maintenance_Menu}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-30T15:30:23.101333" elapsed="3.754392"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-30T15:30:31.856884" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-30T15:30:26.856722" elapsed="5.000162"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${ADD_NEW_CALL}</arg>
<arg>5</arg>
<arg>Main</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-30T15:30:31.857448" elapsed="0.055556"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-30T15:30:31.992293" level="INFO">Current page contains text 'Site Maintenance'.</msg>
<arg>Site Maintenance</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-30T15:30:31.914003" elapsed="0.078290"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${Site_Maintenance_table_ele}</arg>
<arg>15s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-30T15:30:31.993297" elapsed="0.054112"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-30T15:30:35.049283" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-30T15:30:32.048011" elapsed="3.001272"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-30T15:30:35.084396" level="INFO">Element 'xpath=//*[@id="root"]/div/table/tbody/tr[1]' is displayed.</msg>
<arg>xpath=//*[@id="root"]/div/table/tbody/tr[1]</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-30T15:30:35.049283" elapsed="0.035113"/>
</kw>
<msg time="2025-06-30T15:30:35.084396" level="INFO">${table_has_data} = True</msg>
<var>${table_has_data}</var>
<arg>Element Should Be Visible</arg>
<arg>xpath=//*[@id="root"]/div/table/tbody/tr[1]</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-30T15:30:35.049283" elapsed="0.035113"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>not ${table_has_data}</arg>
<arg>Sleep</arg>
<arg>5s</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-30T15:30:35.084396" elapsed="0.000999"/>
</kw>
<status status="PASS" start="2025-06-30T15:30:23.100335" elapsed="11.985060"/>
</kw>
<kw name="Then The user uses the PREV and NEXT buttons on page navigation" owner="Site_Maintenance">
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-30T15:30:35.120172" level="INFO">${footer_text} = Showing 1 to 5 of 15 rows</msg>
<var>${footer_text}</var>
<arg>xpath=//*[@id="root"]/div/div/div/div[1]/span</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-30T15:30:35.085395" elapsed="0.034777"/>
</kw>
<kw name="Extract Total Rows From Footer" owner="Site_Maintenance">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-30T15:30:35.125171" level="INFO">Footer Text: Showing 1 to 5 of 15 rows</msg>
<arg>Footer Text: ${footer_text}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-30T15:30:35.124169" elapsed="0.001002"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-30T15:30:35.126173" level="INFO">${pattern} = \d+</msg>
<var>${pattern}</var>
<arg>\\d+</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-30T15:30:35.126173" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-30T15:30:35.127751" level="INFO">Regex Pattern: \d+</msg>
<arg>Regex Pattern: ${pattern}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-30T15:30:35.127751" elapsed="0.000963"/>
</kw>
<kw name="Get Regexp Matches" owner="String">
<msg time="2025-06-30T15:30:35.130730" level="INFO">${matches} = ['1', '5', '15']</msg>
<var>${matches}</var>
<arg>${footer_text}</arg>
<arg>${pattern}</arg>
<doc>Returns a list of all non-overlapping matches in the given string.</doc>
<status status="PASS" start="2025-06-30T15:30:35.128714" elapsed="0.002016"/>
</kw>
<kw name="Log Many" owner="BuiltIn">
<msg time="2025-06-30T15:30:35.133732" level="INFO">['1', '5', '15']</msg>
<arg>${matches}</arg>
<doc>Logs the given messages as separate entries using the INFO level.</doc>
<status status="PASS" start="2025-06-30T15:30:35.132733" elapsed="0.000999"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>${matches} == []</arg>
<arg>Fail</arg>
<arg>No matches found in the footer text: ${footer_text}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-30T15:30:35.134728" elapsed="0.001001"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-30T15:30:35.146110" level="INFO">${total_rows} = 15</msg>
<var>${total_rows}</var>
<arg>${matches[-1]}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-30T15:30:35.135729" elapsed="0.010381"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-30T15:30:35.148660" level="INFO">Total rows extracted: 15</msg>
<arg>Total rows extracted: ${total_rows}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-30T15:30:35.147633" elapsed="0.002028"/>
</kw>
<return>
<value>${total_rows}</value>
<status status="PASS" start="2025-06-30T15:30:35.149661" elapsed="0.000000"/>
</return>
<msg time="2025-06-30T15:30:35.151251" level="INFO">${total_rows} = 15</msg>
<var>${total_rows}</var>
<arg>${footer_text}</arg>
<status status="PASS" start="2025-06-30T15:30:35.122170" elapsed="0.029081"/>
</kw>
<kw name="Calculate Total Pages" owner="Site_Maintenance">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-30T15:30:35.155210" level="INFO">${total_pages} = 3</msg>
<var>${total_pages}</var>
<arg>int(math.ceil(${total_rows} / ${rows_per_page}))</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-30T15:30:35.154211" elapsed="0.000999"/>
</kw>
<return>
<value>${total_pages}</value>
<status status="PASS" start="2025-06-30T15:30:35.155210" elapsed="0.000000"/>
</return>
<msg time="2025-06-30T15:30:35.155210" level="INFO">${total_pages} = 3</msg>
<var>${total_pages}</var>
<arg>${total_rows}</arg>
<arg>${rows_per_page}</arg>
<status status="PASS" start="2025-06-30T15:30:35.153209" elapsed="0.002001"/>
</kw>
<for flavor="IN RANGE">
<iter>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-30T15:30:35.156209" level="INFO">Navigating to Page 1 of 3</msg>
<arg>Navigating to Page ${page} of ${total_pages}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-30T15:30:35.156209" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Enabled" owner="SeleniumLibrary">
<arg>xpath=//*[text()[normalize-space(.)='Next']]</arg>
<doc>Verifies that element identified by ``locator`` is enabled.</doc>
<status status="PASS" start="2025-06-30T15:30:35.156209" elapsed="0.070622"/>
</kw>
<msg time="2025-06-30T15:30:35.226831" level="INFO">${is_next_enabled} = True</msg>
<var>${is_next_enabled}</var>
<arg>Element Should Be Enabled</arg>
<arg>xpath=//*[text()[normalize-space(.)='Next']]</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-30T15:30:35.156209" elapsed="0.070622"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-30T15:30:35.227828" level="INFO">Is Next Button Enabled: True</msg>
<arg>Is Next Button Enabled: ${is_next_enabled}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-30T15:30:35.227828" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-06-30T15:30:35.228355" level="INFO">Clicking element 'xpath=//*[text()[normalize-space(.)='Next']]'.</msg>
<arg>xpath=//*[text()[normalize-space(.)='Next']]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-30T15:30:35.228355" elapsed="0.105640"/>
</kw>
<arg>${is_next_enabled}</arg>
<arg>Click Element</arg>
<arg>xpath=//*[text()[normalize-space(.)='Next']]</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-30T15:30:35.228355" elapsed="0.105640"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-30T15:30:36.335529" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-30T15:30:35.334997" elapsed="1.001054"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Enabled" owner="SeleniumLibrary">
<msg time="2025-06-30T15:30:36.582618" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-3.png"&gt;&lt;img src="selenium-screenshot-3.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2025-06-30T15:30:36.583610" level="FAIL">Element with locator 'xpath=//*[text()[normalize-space(.)='Previous']]' not found.</msg>
<arg>xpath=//*[text()[normalize-space(.)='Previous']]</arg>
<doc>Verifies that element identified by ``locator`` is enabled.</doc>
<status status="FAIL" start="2025-06-30T15:30:36.337569" elapsed="0.247046">Element with locator 'xpath=//*[text()[normalize-space(.)='Previous']]' not found.</status>
</kw>
<msg time="2025-06-30T15:30:36.584615" level="INFO">${is_prev_enabled} = False</msg>
<var>${is_prev_enabled}</var>
<arg>Element Should Be Enabled</arg>
<arg>xpath=//*[text()[normalize-space(.)='Previous']]</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-30T15:30:36.336051" elapsed="0.248564"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-30T15:30:36.584615" level="INFO">Is Prev Button Enabled: False</msg>
<arg>Is Prev Button Enabled: ${is_prev_enabled}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-30T15:30:36.584615" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-30T15:30:36.996748" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-4.png"&gt;&lt;img src="selenium-screenshot-4.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2025-06-30T15:30:36.996748" level="FAIL">Page should have contained text 'Page 1' but did not.</msg>
<arg>Page ${page}</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="FAIL" start="2025-06-30T15:30:36.585617" elapsed="0.412215">Page should have contained text 'Page 1' but did not.</status>
</kw>
<msg time="2025-06-30T15:30:36.997832" level="INFO">${page_indicator_present} = False</msg>
<var>${page_indicator_present}</var>
<arg>Page Should Contain</arg>
<arg>Page ${page}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-30T15:30:36.584615" elapsed="0.413217"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-30T15:30:36.998806" level="WARN">Page indicator 'Page 1' not found, but navigation successful</msg>
<arg>Page indicator 'Page ${page}' not found, but navigation successful</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-30T15:30:36.998806" elapsed="0.001000"/>
</kw>
<arg>not ${page_indicator_present}</arg>
<arg>Log</arg>
<arg>Page indicator 'Page ${page}' not found, but navigation successful</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-30T15:30:36.997832" elapsed="0.001974"/>
</kw>
<var name="${page}">1</var>
<status status="PASS" start="2025-06-30T15:30:35.156209" elapsed="1.843597"/>
</iter>
<iter>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-30T15:30:36.999806" level="INFO">Navigating to Page 2 of 3</msg>
<arg>Navigating to Page ${page} of ${total_pages}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-30T15:30:36.999806" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Enabled" owner="SeleniumLibrary">
<arg>xpath=//*[text()[normalize-space(.)='Next']]</arg>
<doc>Verifies that element identified by ``locator`` is enabled.</doc>
<status status="PASS" start="2025-06-30T15:30:36.999806" elapsed="0.060981"/>
</kw>
<msg time="2025-06-30T15:30:37.061813" level="INFO">${is_next_enabled} = True</msg>
<var>${is_next_enabled}</var>
<arg>Element Should Be Enabled</arg>
<arg>xpath=//*[text()[normalize-space(.)='Next']]</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-30T15:30:36.999806" elapsed="0.062007"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-30T15:30:37.061813" level="INFO">Is Next Button Enabled: True</msg>
<arg>Is Next Button Enabled: ${is_next_enabled}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-30T15:30:37.061813" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-06-30T15:30:37.061813" level="INFO">Clicking element 'xpath=//*[text()[normalize-space(.)='Next']]'.</msg>
<arg>xpath=//*[text()[normalize-space(.)='Next']]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-30T15:30:37.061813" elapsed="0.184576"/>
</kw>
<arg>${is_next_enabled}</arg>
<arg>Click Element</arg>
<arg>xpath=//*[text()[normalize-space(.)='Next']]</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-30T15:30:37.061813" elapsed="0.184576"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-30T15:30:38.249603" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-30T15:30:37.247995" elapsed="1.001608"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Enabled" owner="SeleniumLibrary">
<msg time="2025-06-30T15:30:38.430966" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-5.png"&gt;&lt;img src="selenium-screenshot-5.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2025-06-30T15:30:38.430966" level="FAIL">Element with locator 'xpath=//*[text()[normalize-space(.)='Previous']]' not found.</msg>
<arg>xpath=//*[text()[normalize-space(.)='Previous']]</arg>
<doc>Verifies that element identified by ``locator`` is enabled.</doc>
<status status="FAIL" start="2025-06-30T15:30:38.251578" elapsed="0.180379">Element with locator 'xpath=//*[text()[normalize-space(.)='Previous']]' not found.</status>
</kw>
<msg time="2025-06-30T15:30:38.431957" level="INFO">${is_prev_enabled} = False</msg>
<var>${is_prev_enabled}</var>
<arg>Element Should Be Enabled</arg>
<arg>xpath=//*[text()[normalize-space(.)='Previous']]</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-30T15:30:38.250585" elapsed="0.181372"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-30T15:30:38.432959" level="INFO">Is Prev Button Enabled: False</msg>
<arg>Is Prev Button Enabled: ${is_prev_enabled}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-30T15:30:38.431957" elapsed="0.001002"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-30T15:30:38.820716" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-6.png"&gt;&lt;img src="selenium-screenshot-6.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2025-06-30T15:30:38.820716" level="FAIL">Page should have contained text 'Page 2' but did not.</msg>
<arg>Page ${page}</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="FAIL" start="2025-06-30T15:30:38.434979" elapsed="0.386736">Page should have contained text 'Page 2' but did not.</status>
</kw>
<msg time="2025-06-30T15:30:38.821715" level="INFO">${page_indicator_present} = False</msg>
<var>${page_indicator_present}</var>
<arg>Page Should Contain</arg>
<arg>Page ${page}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-30T15:30:38.433960" elapsed="0.387755"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-30T15:30:38.822714" level="WARN">Page indicator 'Page 2' not found, but navigation successful</msg>
<arg>Page indicator 'Page ${page}' not found, but navigation successful</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-30T15:30:38.822714" elapsed="0.001002"/>
</kw>
<arg>not ${page_indicator_present}</arg>
<arg>Log</arg>
<arg>Page indicator 'Page ${page}' not found, but navigation successful</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-30T15:30:38.821715" elapsed="0.002998"/>
</kw>
<var name="${page}">2</var>
<status status="PASS" start="2025-06-30T15:30:36.999806" elapsed="1.824907"/>
</iter>
<var>${page}</var>
<value>1</value>
<value>${total_pages}</value>
<status status="PASS" start="2025-06-30T15:30:35.155210" elapsed="3.669503"/>
</for>
<for flavor="IN RANGE">
<iter>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-30T15:30:38.826707" level="INFO">Navigating back to Page 3 of 3</msg>
<arg>Navigating back to Page ${page} of ${total_pages}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-30T15:30:38.826707" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Enabled" owner="SeleniumLibrary">
<msg time="2025-06-30T15:30:38.991632" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-7.png"&gt;&lt;img src="selenium-screenshot-7.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2025-06-30T15:30:38.991632" level="FAIL">Element with locator 'xpath=//*[text()[normalize-space(.)='Previous']]' not found.</msg>
<arg>xpath=//*[text()[normalize-space(.)='Previous']]</arg>
<doc>Verifies that element identified by ``locator`` is enabled.</doc>
<status status="FAIL" start="2025-06-30T15:30:38.827717" elapsed="0.164914">Element with locator 'xpath=//*[text()[normalize-space(.)='Previous']]' not found.</status>
</kw>
<msg time="2025-06-30T15:30:38.992631" level="INFO">${is_prev_enabled} = False</msg>
<var>${is_prev_enabled}</var>
<arg>Element Should Be Enabled</arg>
<arg>xpath=//*[text()[normalize-space(.)='Previous']]</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-30T15:30:38.827717" elapsed="0.164914"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-30T15:30:38.992631" level="INFO">Is Prev Button Enabled: False</msg>
<arg>Is Prev Button Enabled: ${is_prev_enabled}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-30T15:30:38.992631" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>${is_prev_enabled}</arg>
<arg>Click Element</arg>
<arg>xpath=//*[text()[normalize-space(.)='Previous']]</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-30T15:30:38.992631" elapsed="0.001000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-30T15:30:39.995168" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-30T15:30:38.993631" elapsed="1.002056"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Enabled" owner="SeleniumLibrary">
<arg>xpath=//*[text()[normalize-space(.)='Next']]</arg>
<doc>Verifies that element identified by ``locator`` is enabled.</doc>
<status status="PASS" start="2025-06-30T15:30:39.997715" elapsed="0.070969"/>
</kw>
<msg time="2025-06-30T15:30:40.068684" level="INFO">${is_next_enabled} = True</msg>
<var>${is_next_enabled}</var>
<arg>Element Should Be Enabled</arg>
<arg>xpath=//*[text()[normalize-space(.)='Next']]</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-30T15:30:39.996726" elapsed="0.071958"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-30T15:30:40.069687" level="INFO">Is Next Button Enabled: True</msg>
<arg>Is Next Button Enabled: ${is_next_enabled}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-30T15:30:40.068684" elapsed="0.001003"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-30T15:30:40.388439" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-8.png"&gt;&lt;img src="selenium-screenshot-8.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2025-06-30T15:30:40.388439" level="FAIL">Page should have contained text 'Page 3' but did not.</msg>
<arg>Page ${page}</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="FAIL" start="2025-06-30T15:30:40.069687" elapsed="0.319780">Page should have contained text 'Page 3' but did not.</status>
</kw>
<msg time="2025-06-30T15:30:40.389467" level="INFO">${page_indicator_present} = False</msg>
<var>${page_indicator_present}</var>
<arg>Page Should Contain</arg>
<arg>Page ${page}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-30T15:30:40.069687" elapsed="0.319780"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-30T15:30:40.390492" level="WARN">Page indicator 'Page 3' not found, but navigation successful</msg>
<arg>Page indicator 'Page ${page}' not found, but navigation successful</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-30T15:30:40.389467" elapsed="0.002022"/>
</kw>
<arg>not ${page_indicator_present}</arg>
<arg>Log</arg>
<arg>Page indicator 'Page ${page}' not found, but navigation successful</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-30T15:30:40.389467" elapsed="0.002022"/>
</kw>
<var name="${page}">3</var>
<status status="PASS" start="2025-06-30T15:30:38.825713" elapsed="1.565776"/>
</iter>
<iter>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-30T15:30:40.391489" level="INFO">Navigating back to Page 2 of 3</msg>
<arg>Navigating back to Page ${page} of ${total_pages}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-30T15:30:40.391489" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Enabled" owner="SeleniumLibrary">
<msg time="2025-06-30T15:30:40.524200" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-9.png"&gt;&lt;img src="selenium-screenshot-9.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2025-06-30T15:30:40.524200" level="FAIL">Element with locator 'xpath=//*[text()[normalize-space(.)='Previous']]' not found.</msg>
<arg>xpath=//*[text()[normalize-space(.)='Previous']]</arg>
<doc>Verifies that element identified by ``locator`` is enabled.</doc>
<status status="FAIL" start="2025-06-30T15:30:40.392490" elapsed="0.132705">Element with locator 'xpath=//*[text()[normalize-space(.)='Previous']]' not found.</status>
</kw>
<msg time="2025-06-30T15:30:40.526189" level="INFO">${is_prev_enabled} = False</msg>
<var>${is_prev_enabled}</var>
<arg>Element Should Be Enabled</arg>
<arg>xpath=//*[text()[normalize-space(.)='Previous']]</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-30T15:30:40.392490" elapsed="0.133699"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-30T15:30:40.526189" level="INFO">Is Prev Button Enabled: False</msg>
<arg>Is Prev Button Enabled: ${is_prev_enabled}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-30T15:30:40.526189" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>${is_prev_enabled}</arg>
<arg>Click Element</arg>
<arg>xpath=//*[text()[normalize-space(.)='Previous']]</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-30T15:30:40.526189" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-30T15:30:41.526749" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-30T15:30:40.526189" elapsed="1.000560"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Enabled" owner="SeleniumLibrary">
<arg>xpath=//*[text()[normalize-space(.)='Next']]</arg>
<doc>Verifies that element identified by ``locator`` is enabled.</doc>
<status status="PASS" start="2025-06-30T15:30:41.528818" elapsed="0.054838"/>
</kw>
<msg time="2025-06-30T15:30:41.583656" level="INFO">${is_next_enabled} = True</msg>
<var>${is_next_enabled}</var>
<arg>Element Should Be Enabled</arg>
<arg>xpath=//*[text()[normalize-space(.)='Next']]</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-30T15:30:41.527877" elapsed="0.057277"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-30T15:30:41.585154" level="INFO">Is Next Button Enabled: True</msg>
<arg>Is Next Button Enabled: ${is_next_enabled}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-30T15:30:41.585154" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-30T15:30:42.009050" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-10.png"&gt;&lt;img src="selenium-screenshot-10.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2025-06-30T15:30:42.010051" level="FAIL">Page should have contained text 'Page 2' but did not.</msg>
<arg>Page ${page}</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="FAIL" start="2025-06-30T15:30:41.585154" elapsed="0.424897">Page should have contained text 'Page 2' but did not.</status>
</kw>
<msg time="2025-06-30T15:30:42.010051" level="INFO">${page_indicator_present} = False</msg>
<var>${page_indicator_present}</var>
<arg>Page Should Contain</arg>
<arg>Page ${page}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-30T15:30:41.585154" elapsed="0.424897"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-30T15:30:42.011049" level="WARN">Page indicator 'Page 2' not found, but navigation successful</msg>
<arg>Page indicator 'Page ${page}' not found, but navigation successful</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-30T15:30:42.011049" elapsed="0.000997"/>
</kw>
<arg>not ${page_indicator_present}</arg>
<arg>Log</arg>
<arg>Page indicator 'Page ${page}' not found, but navigation successful</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-30T15:30:42.011049" elapsed="0.001999"/>
</kw>
<var name="${page}">2</var>
<status status="PASS" start="2025-06-30T15:30:40.391489" elapsed="1.621559"/>
</iter>
<var>${page}</var>
<value>${total_pages}</value>
<value>1</value>
<value>-1</value>
<status status="PASS" start="2025-06-30T15:30:38.824713" elapsed="3.188335"/>
</for>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-30T15:30:42.013048" level="INFO">The user successfully validated the Prev/Next buttons on Site Maintenance</msg>
<arg>The user successfully validated the Prev/Next buttons on Site Maintenance</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-30T15:30:42.013048" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-30T15:30:42.014047" level="INFO">The user successfully validated the Prev/Next buttons on Site Maintenance</msg>
<arg>The user successfully validated the Prev/Next buttons on Site Maintenance</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-30T15:30:42.013048" elapsed="0.000999"/>
</kw>
<status status="PASS" start="2025-06-30T15:30:35.085395" elapsed="6.928652"/>
</kw>
<arg>Validating the PREV and NEXT Buttons</arg>
<arg>VMS_UAT</arg>
<status status="PASS" start="2025-06-30T15:29:27.359161" elapsed="74.654886"/>
</kw>
<doc>Validating the PREV and NEXT Buttons</doc>
<tag>Site Maintenance</tag>
<tag>VMS HEALTHCHECK</tag>
<status status="PASS" start="2025-06-30T15:29:27.356930" elapsed="74.657117"/>
</test>
<doc>Validating the PREV and NEXT Buttons on site maintenance</doc>
<status status="PASS" start="2025-06-30T15:29:19.913004" elapsed="82.103049"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="1" fail="0" skip="0">Site Maintenance</stat>
<stat pass="1" fail="0" skip="0">VMS HEALTHCHECK</stat>
</tag>
<suite>
<stat name="VMS Portal" id="s1" pass="1" fail="0" skip="0">VMS Portal</stat>
</suite>
</statistics>
<errors>
<msg time="2025-06-30T15:29:24.254653" level="ERROR">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\SetEnvironmentVariales.robot' on line 14: Importing library 'C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py' failed: ModuleNotFoundError: No module named 'acintegration'
Traceback (most recent call last):
  File "C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py", line 1, in &lt;module&gt;
    from acintegration.QMetryIntegration import QMetryIntegration
PYTHONPATH:
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\robot.exe
  C:\Alternative
  C:\Alternative\Alternative_Physical_Channels_QA
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages</msg>
<msg time="2025-06-30T15:29:24.276186" level="WARN">Imported library 'C:\Alternative\Alternative_Physical_Channels_QA\future_fit_architecture_portal_BinTables\keywords\controllers\resources\RestRequestsUnMarshal.py' contains no keywords.</msg>
<msg time="2025-06-30T15:29:26.991044" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\common_keywords.robot' on line 117: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-30T15:29:26.991044" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\common_keywords.robot' on line 137: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-30T15:29:27.078671" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\Dashboard.robot' on line 179: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-30T15:29:27.079675" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\Dashboard.robot' on line 197: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-30T15:29:27.079675" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\Dashboard.robot' on line 227: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-30T15:29:27.080675" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\Dashboard.robot' on line 257: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-30T15:29:27.080675" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\Dashboard.robot' on line 272: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-30T15:29:27.082085" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\Dashboard.robot' on line 281: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-30T15:29:27.082085" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\Dashboard.robot' on line 292: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-30T15:29:27.082611" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\Dashboard.robot' on line 305: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-30T15:29:27.083397" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\Dashboard.robot' on line 435: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-30T15:29:27.263111" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\DatabaseConnector.robot' on line 355: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-30T15:29:27.263646" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\DatabaseConnector.robot' on line 389: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-30T15:29:27.314584" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\Site_Maintenance.robot' on line 127: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-30T15:29:27.318611" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\Site_Maintenance.robot' on line 133: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-30T15:29:27.325171" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\Site_Maintenance.robot' on line 418: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-30T15:29:28.526851" level="WARN">There was error during termination of process</msg>
<msg time="2025-06-30T15:29:28.531868" level="WARN">exexcutable_path is being deprecated. Please use service to configure the driver's executable_path as per documentation.</msg>
<msg time="2025-06-30T15:29:46.929590" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-30T15:29:56.965172" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-30T15:30:01.999773" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-30T15:30:36.998806" level="WARN">Page indicator 'Page 1' not found, but navigation successful</msg>
<msg time="2025-06-30T15:30:38.822714" level="WARN">Page indicator 'Page 2' not found, but navigation successful</msg>
<msg time="2025-06-30T15:30:40.390492" level="WARN">Page indicator 'Page 3' not found, but navigation successful</msg>
<msg time="2025-06-30T15:30:42.011049" level="WARN">Page indicator 'Page 2' not found, but navigation successful</msg>
</errors>
</robot>
