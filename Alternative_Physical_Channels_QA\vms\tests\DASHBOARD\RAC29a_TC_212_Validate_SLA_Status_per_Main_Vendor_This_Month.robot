*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                                        VMS HEALTHCHECK    DASHBOARD
Suite Setup                                         Set up environment variables
Test Teardown                                       The user logs out of VMS

Documentation                                       SLA Status Per Main Vendor- This Month Validation 

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem
Library                                             DatabaseLibrary


#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../common_utilities/Login.robot 
Resource                                            ../../../common_utilities/Logout.robot    
Resource                                            ../../keywords/VMSPage/Dashboard.robot
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot

*** Keywords ***
Dashboard Validation
    [Arguments]  ${DOCUMENTATION}  ${TEST_ENVIRONMENT}    
    Set Test Documentation  ${DOCUMENTATION} 

    Given The user logs into the VMS Web Application     ${TEST_ENVIRONMENT}  

    When The user lands on the dashboard page

    And The user reads the dashboard details for SLA Status Per Main Vendor for this month

    And The user reads the database details for SLA Status Per Main Vendor for this month

    Then The Database details must be the same as Front End details for SLA Status Per Main Vendor for this month

*** Test Cases ***
Validate SLA Status per Main Vendor- This Month
    Dashboard Validation    Validates SLA Status Per Main Vendor for this month    VMS_UAT