server-api: 2025-07-01 05:47:35 UTC pid: 6380 - MainThread - INFO - robotframework_ls.__main__
Arguments: ['-v', '--log-file=c:\\Alternative\\robot_lsp.log.3.api']

server-api: 2025-07-01 05:47:35 UTC pid: 6380 - MainThread - INFO - robotframework_ls.__main__
Python: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)] - lsp: 1.13.0 (<module 'robotframework_ls' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\robotframework_ls\\__init__.py'>) - platform: win32 - sys.prefix: C:\Users\<USER>\AppData\Local\Programs\Python\Python312 - sys.executable: C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe

server-api: 2025-07-01 05:47:35 UTC pid: 6380 - MainThread - INFO - robotframework_ls.__main__
CPUs: 12

server-api: 2025-07-01 05:47:35 UTC pid: 6380 - MainThread - INFO - robocorp_ls_core.python_ls
Starting RobotFrameworkServerApiWithObserver IO language server. pid: 6380

server-api: 2025-07-01 05:47:36 UTC pid: 6380 - MainThread - INFO - robotframework_ls.impl.libspec_manager
User libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\user

server-api: 2025-07-01 05:47:36 UTC pid: 6380 - MainThread - INFO - robotframework_ls.impl.libspec_manager
Builtins libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\builtins

server-api: 2025-07-01 05:47:36 UTC pid: 6380 - MainThread - INFO - robotframework_ls.impl.libspec_manager
Cache libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\cache

