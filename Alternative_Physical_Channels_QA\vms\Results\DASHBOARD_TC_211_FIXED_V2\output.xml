<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.2.2 (Python 3.12.10 on win32)" generated="2025-06-30T14:11:18.235147" rpa="false" schemaversion="5">
<suite id="s1" name="VMS Portal" source="C:\Alternative\Alternative_Physical_Channels_QA\vms\tests\DASHBOARD\RAC29a_TC_211_Validate_SLA_Status_per_Main_Vendor_This_Year.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-30T14:11:21.322458" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-30T14:11:21.322458" elapsed="0.001027"/>
</kw>
<arg>'${UPLOAD_TEST_STEPS}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${UPLOAD_TEST_STEPS} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-30T14:11:21.322458" elapsed="0.001027"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-30T14:11:21.324484" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'Bin_Tables.xml'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-30T14:11:21.324484" elapsed="0.000000"/>
</kw>
<arg>'${ROBOT_FILE_PATH}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${ROBOT_FILE_PATH} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-30T14:11:21.323485" elapsed="0.000999"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-30T14:11:21.325482" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-30T14:11:21.325482" elapsed="0.000000"/>
</kw>
<arg>'${IS_HEADLESS_BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${IS_HEADLESS_BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-30T14:11:21.324484" elapsed="0.000998"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-30T14:11:21.326481" level="INFO">Environment variable  does not exist.</msg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-30T14:11:21.326481" elapsed="0.000000"/>
</kw>
<arg>'${BASE_URL}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-30T14:11:21.326481" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-30T14:11:21.327483" level="INFO">Environment variable 'BROWSER' set to value 'edge'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-30T14:11:21.327483" elapsed="0.000000"/>
</kw>
<arg>'${BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-30T14:11:21.326481" elapsed="0.001002"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-30T14:11:21.328480" level="INFO">Environment variable 'SUITE_DIRECTORY' set to value 'vms/data'.</msg>
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-30T14:11:21.327483" elapsed="0.000997"/>
</kw>
<arg>'${SUITE_DIRECTORY}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'SUITE_DIRECTORY', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-30T14:11:21.327483" elapsed="0.000997"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-30T14:11:21.328480" level="INFO">Environment variable called 'MS_USERNAME', does not exist.</msg>
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-30T14:11:21.328480" elapsed="0.000000"/>
</kw>
<arg>'${MS_USERNAME}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_USERNAME</arg>
<arg>${MS_USERNAME}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-30T14:11:21.328480" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-30T14:11:21.328480" level="INFO">Environment variable called 'MS_PASSWORD', does not exist.</msg>
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-30T14:11:21.328480" elapsed="0.000000"/>
</kw>
<arg>'${MS_PASSWORD}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_PASSWORD</arg>
<arg>${MS_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-30T14:11:21.328480" elapsed="0.000998"/>
</kw>
<if>
<branch type="IF" condition="'${TEST_CASE_ID}' != ''">
<kw name="Get Test Case ID" owner="SetEnvironmentVariales">
<var>${TEST_CASE_ID}</var>
<arg>${TEST_CASE_ID}</arg>
<status status="NOT RUN" start="2025-06-30T14:11:21.329478" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Test Case ID: ${TEST_CASE_ID}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-06-30T14:11:21.329478" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-30T14:11:21.329478" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-06-30T14:11:21.329478" elapsed="0.000000"/>
</if>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-30T14:11:21.329478" level="INFO">Environment variable called 'TEST_CASE_ID', does not exist.</msg>
<arg>Environment variable called 'TEST_CASE_ID', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-30T14:11:21.329478" elapsed="0.000000"/>
</kw>
<arg>'${TEST_CASE_ID}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>TEST_CASE_ID</arg>
<arg>${TEST_CASE_ID}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'TEST_CASE_ID', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-30T14:11:21.329478" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-30T14:11:21.321022" elapsed="0.008456"/>
</kw>
<test id="s1-t1" name="Validate SLA Status per Main Vendor- This Year" line="39">
<kw name="Dashboard Validation">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2025-06-30T14:11:21.331103" level="INFO">Set test documentation to:
Validates SLA Status Per Main Vendor for this year</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2025-06-30T14:11:21.331103" elapsed="0.000000"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-30T14:11:21.332015" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-30T14:11:21.332015" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-30T14:11:21.332015" elapsed="0.001003"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-30T14:11:21.333018" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="VMS Windows system Login" owner="Login">
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-30T14:11:21.335014" level="FAIL">Environment variable 'BASE_URL' does not exist.</msg>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="FAIL" start="2025-06-30T14:11:21.335014" elapsed="0.000000">Environment variable 'BASE_URL' does not exist.</status>
</kw>
<msg time="2025-06-30T14:11:21.335014" level="INFO">${url_exists_on_env_var} = False</msg>
<var>${url_exists_on_env_var}</var>
<arg>Get Environment Variable</arg>
<arg>BASE_URL</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-30T14:11:21.334015" elapsed="0.000999"/>
</kw>
<if>
<branch type="IF" condition="${url_exists_on_env_var}">
<kw name="Get Environment Variable" owner="OperatingSystem">
<var>${BASE_URL}</var>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="NOT RUN" start="2025-06-30T14:11:21.335014" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-30T14:11:21.335014" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-30T14:11:21.335014" level="INFO">${BASE_URL} = VMS_UAT</msg>
<var>${BASE_URL}</var>
<arg>${URL}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-30T14:11:21.335014" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-30T14:11:21.335014" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-06-30T14:11:21.335014" elapsed="0.000000"/>
</if>
<kw name="Read Config Property" owner="CommonUtils">
<msg time="2025-06-30T14:11:21.352284" level="INFO">Property value fetched is:  https://vms.uat.absa.africa/</msg>
<msg time="2025-06-30T14:11:21.352284" level="INFO">${base_url} = https://vms.uat.absa.africa/</msg>
<var>${base_url}</var>
<arg>${BASE_URL}</arg>
<status status="PASS" start="2025-06-30T14:11:21.335014" elapsed="0.017270"/>
</kw>
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-30T14:11:21.354306" level="INFO">${PROCESS_NAME_LowerCase} = edge</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-30T14:11:21.354306" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-30T14:11:21.354306" level="INFO">${PROCESS_NAME_LowerCase} = msedge</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-30T14:11:21.354306" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-30T14:11:21.354306" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-06-30T14:11:21.354306" elapsed="0.000000"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-06-30T14:11:21.354306" level="INFO">${handle} = msedge.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-06-30T14:11:21.354306" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-30T14:11:21.355301" elapsed="0.000000"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2025-06-30T14:11:21.567735" level="INFO">Running command 'taskkill /F /IM msedge.exe 2&gt;&amp;1'.</msg>
<msg time="2025-06-30T14:11:22.185402" level="INFO">${rc_code} = 128</msg>
<msg time="2025-06-30T14:11:22.185402" level="INFO">${output} = ERROR: The process "msedge.exe" not found.</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2025-06-30T14:11:21.355301" elapsed="0.830101"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-30T14:11:22.186405" level="WARN">There was error during termination of process</msg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-30T14:11:22.186405" elapsed="0.001000"/>
</kw>
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-30T14:11:22.185402" elapsed="0.002003"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2025-06-30T14:11:21.353308" elapsed="0.834097"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-30T14:11:22.188405" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2025-06-30T14:11:22.187405" elapsed="0.001000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-06-30T14:11:22.188405" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-06-30T14:11:22.188405" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-06-30T14:11:22.188405" level="INFO">${browser_name} = EDGE</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-06-30T14:11:22.188405" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-30T14:11:22.189407" level="INFO">${user_home} = C:\Users\<USER>\Users\AB038N8</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-30T14:11:22.189407" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-30T14:11:22.191004" level="INFO">${edgedriver_path} = C:/bin/msedgedriver.exe</msg>
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-30T14:11:22.191004" elapsed="0.000000"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<msg time="2025-06-30T14:11:22.191004" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x000001E3CD0D6780&gt;</msg>
<var>${edge_options}</var>
<status status="PASS" start="2025-06-30T14:11:22.191004" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2025-06-30T14:11:22.191004" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-06-30T14:11:22.191004" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-30T14:11:22.191004" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-30T14:11:22.192032" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x000001E3CD0D7860&gt;</msg>
<var>${edge_options}</var>
<arg>sys.modules['selenium.webdriver'].EdgeOptions()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-30T14:11:22.192032" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-30T14:11:22.192032" level="INFO">${root} = C:\Users\<USER>\</msg>
<var>${root}</var>
<arg>${user_home}\\</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-30T14:11:22.192032" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-30T14:11:22.192032" level="INFO">${path} = Downloads</msg>
<var>${path}</var>
<arg>Downloads</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-30T14:11:22.192032" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-30T14:11:22.192032" level="INFO">${downl_path} = C:\Users\<USER>\Downloads</msg>
<var>${downl_path}</var>
<arg>${root}${path}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-30T14:11:22.192032" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2025-06-30T14:11:22.193028" level="INFO">${prefs} = {'download.default_directory': 'C:\\Users\\<USER>\\Downloads', 'download.directory_upgrade': 'True', 'savefile.default_directory': 'C:\\Users\\<USER>\\Downloads', 'download.prompt_for_download': 'Fa...</msg>
<var>${prefs}</var>
<arg>download.default_directory=${downl_path}</arg>
<arg>download.directory_upgrade=True</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>savefile.default_directory=${downl_path}</arg>
<arg>download.prompt_for_download=False</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2025-06-30T14:11:22.193028" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${edge_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2025-06-30T14:11:22.193028" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<msg time="2025-06-30T14:11:22.194028" level="WARN">exexcutable_path is being deprecated. Please use service to configure the driver's executable_path as per documentation.</msg>
<msg time="2025-06-30T14:11:22.194028" level="INFO">Opening browser 'edge' to base url 'about:blank'.</msg>
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="PASS" start="2025-06-30T14:11:22.193028" elapsed="5.276519"/>
</kw>
<status status="PASS" start="2025-06-30T14:11:22.191004" elapsed="5.278543"/>
</branch>
<status status="PASS" start="2025-06-30T14:11:22.191004" elapsed="5.278543"/>
</if>
<status status="PASS" start="2025-06-30T14:11:22.189407" elapsed="5.281665"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-30T14:11:27.471154" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-30T14:11:27.472100" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-30T14:11:27.473109" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-30T14:11:27.474136" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-30T14:11:27.473109" elapsed="0.001027"/>
</branch>
<status status="NOT RUN" start="2025-06-30T14:11:27.473109" elapsed="0.001027"/>
</if>
<kw name="Set Variable" owner="BuiltIn">
<var>${root}</var>
<arg>${user_home}\\</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-30T14:11:27.474136" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${path}</var>
<arg>Downloads</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-30T14:11:27.475137" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${downl_path}</var>
<arg>${root}${path}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-30T14:11:27.475137" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>download.directory_upgrade=True</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>savefile.default_directory=${downl_path}</arg>
<arg>download.prompt_for_download=False</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="NOT RUN" start="2025-06-30T14:11:27.475137" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-30T14:11:27.476141" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-30T14:11:27.476141" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-30T14:11:27.476141" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-30T14:11:27.477594" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--incognito</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-30T14:11:27.477594" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-30T14:11:27.477594" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-30T14:11:27.477594" elapsed="0.001030"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-30T14:11:27.478624" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${chrome_options}</arg>
<arg>executable_path=${chromedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-06-30T14:11:27.478624" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-30T14:11:27.471154" elapsed="0.007470"/>
</branch>
<status status="PASS" start="2025-06-30T14:11:22.189407" elapsed="5.289217"/>
</if>
<status status="PASS" start="2025-06-30T14:11:21.353308" elapsed="6.126314"/>
</kw>
<kw name="Load" owner="Login">
<kw name="Maximize Browser Window" owner="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" start="2025-06-30T14:11:27.479622" elapsed="0.069760"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Go To" owner="SeleniumLibrary">
<msg time="2025-06-30T14:11:27.562655" level="INFO">Opening url 'https://vms.uat.absa.africa/'</msg>
<arg>${URL}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<status status="PASS" start="2025-06-30T14:11:27.561415" elapsed="4.202818"/>
</kw>
<arg>'${URL}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${URL}</arg>
<arg>ELSE IF</arg>
<arg>'$${URL}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-30T14:11:27.550966" elapsed="4.213267"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-30T14:11:41.767230" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-30T14:11:31.765261" elapsed="10.001969"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-30T14:11:41.768296" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-30T14:11:41.804796" level="INFO">${element_count_1} = 0</msg>
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-30T14:11:41.768296" elapsed="0.036500"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Navigation.Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-30T14:11:41.805805" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-30T14:11:51.807100" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-30T14:11:41.805805" elapsed="10.001295"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-30T14:11:51.807100" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-30T14:11:51.832999" level="INFO">${element_count_2} = 0</msg>
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-30T14:11:51.808109" elapsed="0.024890"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Navigation.Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-30T14:11:51.832999" elapsed="0.000999"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-30T14:11:56.834307" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-30T14:11:51.833998" elapsed="5.000309"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-30T14:11:56.834307" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-30T14:11:56.862433" level="INFO">${element_count_3} = 0</msg>
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-30T14:11:56.834307" elapsed="0.028126"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-30T14:11:56.862433" elapsed="0.000992"/>
</kw>
<arg>${base_url}</arg>
<status status="PASS" start="2025-06-30T14:11:27.479622" elapsed="29.383803"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-30T14:11:56.918393" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-30T14:11:56.865381" elapsed="0.053012"/>
</kw>
<msg time="2025-06-30T14:11:56.919390" level="INFO">${User_Name_Element_Visible} = True</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-30T14:11:56.864427" elapsed="0.054963"/>
</kw>
<while condition="${User_Name_Element_Visible}">
<iter>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-30T14:11:56.921935" level="INFO">Typing text 'AB038N8' into text field 'name=txtUsername'.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-30T14:11:56.920987" elapsed="0.184857"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-30T14:11:57.106850" elapsed="0.032908"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-30T14:11:57.139758" level="INFO">Typing text '67355870@SitholeBrother' into text field 'name=txtPassword'.</msg>
<arg>${VMS_PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-30T14:11:57.139758" elapsed="0.153910"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-30T14:11:57.295706" level="INFO">Clicking button 'name=btnLogon'.</msg>
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-06-30T14:11:57.294700" elapsed="6.776951"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-30T14:12:06.071824" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-30T14:12:04.071651" elapsed="2.000173"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-30T14:12:06.229431" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-1.png"&gt;&lt;img src="selenium-screenshot-1.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2025-06-30T14:12:06.229431" level="FAIL">Element with locator 'name=txtUsername' not found.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="FAIL" start="2025-06-30T14:12:06.072849" elapsed="0.160166">Element with locator 'name=txtUsername' not found.</status>
</kw>
<msg time="2025-06-30T14:12:06.233015" level="INFO">${User_Name_Element_Visible} = False</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-30T14:12:06.071824" elapsed="0.161191"/>
</kw>
<status status="PASS" start="2025-06-30T14:11:56.919390" elapsed="9.313625"/>
</iter>
<status status="PASS" start="2025-06-30T14:11:56.919390" elapsed="9.313625"/>
</while>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------------ User successfully accessed VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-30T14:12:06.233015" elapsed="0.001387"/>
</kw>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<msg time="2025-06-30T14:12:06.373530" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-2.png"&gt;&lt;img src="selenium-screenshot-2.png" width="800px"&gt;&lt;/a&gt;</msg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="PASS" start="2025-06-30T14:12:06.235395" elapsed="0.138135"/>
</kw>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<status status="PASS" start="2025-06-30T14:11:21.334015" elapsed="45.039515"/>
</kw>
<arg>'${system}' == 'Windows'</arg>
<arg>VMS Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-30T14:11:21.333018" elapsed="45.040512"/>
</kw>
<arg>${TEST_ENVIRONMENT}</arg>
<status status="PASS" start="2025-06-30T14:11:21.331103" elapsed="45.042427"/>
</kw>
<kw name="When The user lands on the dashboard page" owner="Dashboard">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-30T14:12:06.397461" level="INFO">Current page contains text 'Dashboard'.</msg>
<arg>Dashboard</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-30T14:12:06.374529" elapsed="0.022932"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-30T14:12:06.413732" level="INFO">Current page contains text 'Top 10 ATMs with the highest calls'.</msg>
<arg>Top 10 ATMs with the highest calls</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-30T14:12:06.397461" elapsed="0.016271"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-30T14:12:06.431611" level="INFO">Current page contains text 'Main Calls logged for ATMs accross the country'.</msg>
<arg>Main Calls logged for ATMs accross the country</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-30T14:12:06.413732" elapsed="0.017879"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-30T14:12:06.447112" level="INFO">Current page contains text 'Calls logged against Devices'.</msg>
<arg>Calls logged against Devices</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-30T14:12:06.431611" elapsed="0.015501"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-30T14:12:06.459607" level="INFO">Current page contains text 'Top 10 ATMs with the highest calls'.</msg>
<arg>Top 10 ATMs with the highest calls</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-30T14:12:06.448103" elapsed="0.011504"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-30T14:12:06.477981" level="INFO">Current page contains text 'SLA Status per Main Vendor'.</msg>
<arg>SLA Status per Main Vendor</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-30T14:12:06.459607" elapsed="0.018374"/>
</kw>
<status status="PASS" start="2025-06-30T14:12:06.374529" elapsed="0.103452"/>
</kw>
<kw name="And The user reads the dashboard details for SLA Status Per Main Vendor for this year" owner="Dashboard">
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-06-30T14:12:06.478981" level="INFO">Clicking element 'xpath=//*[text()[normalize-space(.)='This Week']]'.</msg>
<arg>xpath=//*[text()[normalize-space(.)='This Week']]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-30T14:12:06.478981" elapsed="0.110305"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-30T14:12:08.589788" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-30T14:12:06.589286" elapsed="2.000502"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-06-30T14:12:08.591319" level="INFO">Clicking element 'xpath=//*[@id='MainContent_btnThisYear']'.</msg>
<arg>xpath=//*[@id='MainContent_btnThisYear']</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-30T14:12:08.591319" elapsed="0.080537"/>
</kw>
<kw name="Wait Until Page Contains" owner="SeleniumLibrary">
<msg time="2025-06-30T14:12:27.312731" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-3.png"&gt;&lt;img src="selenium-screenshot-3.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2025-06-30T14:12:27.312731" level="FAIL">Text 'This Year' did not appear in 15 seconds.</msg>
<arg>This Year</arg>
<arg>timeout=15s</arg>
<doc>Waits until ``text`` appears on the current page.</doc>
<status status="FAIL" start="2025-06-30T14:12:08.671856" elapsed="18.641877">Text 'This Year' did not appear in 15 seconds.</status>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" start="2025-06-30T14:12:27.314733" elapsed="0.000000"/>
</kw>
<kw name="Get WebElements" owner="SeleniumLibrary">
<var>@{g_elements}</var>
<arg>${SLA_STATUS_GRAPH_ELEMENT}</arg>
<doc>Returns a list of WebElement objects matching the ``locator``.</doc>
<status status="NOT RUN" start="2025-06-30T14:12:27.314733" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${all_sla_statuses}</var>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-30T14:12:27.314733" elapsed="0.000000"/>
</kw>
<for flavor="IN">
<iter>
<kw name="Click Element" owner="SeleniumLibrary">
<arg>${element}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-06-30T14:12:27.315735" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>5</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" start="2025-06-30T14:12:27.315735" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>'###############################'</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" start="2025-06-30T14:12:27.315735" elapsed="0.000000"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<var>${ele_text}</var>
<arg>${T_ELE}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-06-30T14:12:27.315735" elapsed="0.000000"/>
</kw>
<kw name="Split String" owner="String">
<var>${ele_text_array}</var>
<arg>${ele_text}</arg>
<doc>Splits the ``string`` using ``separator`` as a delimiter string.</doc>
<status status="NOT RUN" start="2025-06-30T14:12:27.316730" elapsed="0.000000"/>
</kw>
<kw name="Get Length" owner="BuiltIn">
<var>${cnt}</var>
<arg>${ele_text_array}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<status status="NOT RUN" start="2025-06-30T14:12:27.316730" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${counter}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-30T14:12:27.316730" elapsed="0.000000"/>
</kw>
<for flavor="IN RANGE">
<iter>
<kw name="Get From List" owner="Collections">
<var>${element_data}</var>
<arg>${ele_text_array}</arg>
<arg>${index}</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="NOT RUN" start="2025-06-30T14:12:27.317271" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${element_data}' == 'Status:'">
<kw name="Catenate" owner="BuiltIn">
<var>${sla_details}</var>
<arg>${ele_text_array[${counter}-1]}</arg>
<arg>${element_data}</arg>
<arg>${ele_text_array[${counter}+1]}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="NOT RUN" start="2025-06-30T14:12:27.317271" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>SLA STATUS: '${sla_details}'</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" start="2025-06-30T14:12:27.317271" elapsed="0.000000"/>
</kw>
<kw name="Get From List" owner="Collections">
<var>${sla_value}</var>
<arg>${ele_text_array}</arg>
<arg>${counter+1}</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="NOT RUN" start="2025-06-30T14:12:27.317271" elapsed="0.000000"/>
</kw>
<kw name="Catenate" owner="BuiltIn">
<var>${all_sla_statuses}</var>
<arg>${all_sla_statuses}</arg>
<arg>${sla_value}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="NOT RUN" start="2025-06-30T14:12:27.317271" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>Added SLA Status: ${sla_value} to all_sla_statuses</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" start="2025-06-30T14:12:27.317271" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-30T14:12:27.317271" elapsed="0.000000"/>
</branch>
<status status="NOT RUN" start="2025-06-30T14:12:27.317271" elapsed="0.000000"/>
</if>
<kw name="Evaluate" owner="BuiltIn">
<var>${counter}</var>
<arg>${counter} + 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-30T14:12:27.317271" elapsed="0.001023"/>
</kw>
<var name="${index}"/>
<status status="NOT RUN" start="2025-06-30T14:12:27.316730" elapsed="0.001564"/>
</iter>
<var>${index}</var>
<value>${cnt}</value>
<status status="NOT RUN" start="2025-06-30T14:12:27.316730" elapsed="0.001564"/>
</for>
<var name="${element}"/>
<status status="NOT RUN" start="2025-06-30T14:12:27.315735" elapsed="0.002559"/>
</iter>
<var>${element}</var>
<value>@{g_elements}</value>
<status status="NOT RUN" start="2025-06-30T14:12:27.314733" elapsed="0.003561"/>
</for>
<kw name="Evaluate" owner="BuiltIn">
<var>${FrontEnd_sla_statuses_thisyear}</var>
<arg>' '.join(str(int(float(value))) for value in "${all_sla_statuses}".split())</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-30T14:12:27.318294" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Final Cleaned SLA Statuses: ${FrontEnd_sla_statuses_thisyear}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-06-30T14:12:27.318294" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>${FrontEnd_sla_statuses_thisyear}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-06-30T14:12:27.318294" elapsed="0.000000"/>
</kw>
<kw name="Set Suite Variable" owner="BuiltIn">
<arg>${FrontEnd_sla_statuses_thisyear}</arg>
<doc>Makes a variable available everywhere within the scope of the current suite.</doc>
<status status="NOT RUN" start="2025-06-30T14:12:27.318294" elapsed="0.000000"/>
</kw>
<status status="FAIL" start="2025-06-30T14:12:06.478981" elapsed="20.839313">Text 'This Year' did not appear in 15 seconds.</status>
</kw>
<kw name="And The user reads the database details for SLA Status Per Main Vendor for this year" owner="Dashboard">
<doc>Read SLA Status data from database for this year using refactored approach</doc>
<status status="NOT RUN" start="2025-06-30T14:12:27.319277" elapsed="0.000000"/>
</kw>
<kw name="Then The Database details must be the same as Front End details for SLA Status Per Main Vendor for this year" owner="Dashboard">
<doc>Validate SLA Status data between frontend and database for this year</doc>
<status status="NOT RUN" start="2025-06-30T14:12:27.319277" elapsed="0.000000"/>
</kw>
<arg>Validates SLA Status Per Main Vendor for this year</arg>
<arg>VMS_UAT</arg>
<status status="FAIL" start="2025-06-30T14:11:21.331103" elapsed="65.988174">Text 'This Year' did not appear in 15 seconds.</status>
</kw>
<kw name="The user logs out of VMS" owner="Logout" type="TEARDOWN">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_LOGOUT_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-30T14:12:27.320930" elapsed="0.039992"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------------------ Logging out of VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-30T14:12:27.362055" elapsed="0.001030"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-30T14:12:27.365076" level="INFO">Clicking button 'name=ctl00$btnLogout'.</msg>
<arg>${VMS_LOGOUT_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-06-30T14:12:27.364053" elapsed="0.752317"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-30T14:12:31.118853" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-30T14:12:28.117890" elapsed="3.000963"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2025-06-30T14:12:31.119485" elapsed="3.422402"/>
</kw>
<status status="PASS" start="2025-06-30T14:12:27.319277" elapsed="7.222610"/>
</kw>
<doc>Validates SLA Status Per Main Vendor for this year</doc>
<tag>DASHBOARD</tag>
<tag>VMS HEALTHCHECK</tag>
<status status="FAIL" start="2025-06-30T14:11:21.329478" elapsed="73.212409">Text 'This Year' did not appear in 15 seconds.</status>
</test>
<doc>SLA Status Per Main Vendor- This Year Validation</doc>
<status status="FAIL" start="2025-06-30T14:11:18.241688" elapsed="76.302243"/>
</suite>
<statistics>
<total>
<stat pass="0" fail="1" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="0" fail="1" skip="0">DASHBOARD</stat>
<stat pass="0" fail="1" skip="0">VMS HEALTHCHECK</stat>
</tag>
<suite>
<stat name="VMS Portal" id="s1" pass="0" fail="1" skip="0">VMS Portal</stat>
</suite>
</statistics>
<errors>
<msg time="2025-06-30T14:11:20.916101" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\Dashboard.robot' on line 179: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-30T14:11:20.917062" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\Dashboard.robot' on line 197: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-30T14:11:20.917062" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\Dashboard.robot' on line 227: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-30T14:11:20.918098" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\Dashboard.robot' on line 257: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-30T14:11:20.918098" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\Dashboard.robot' on line 272: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-30T14:11:20.919068" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\Dashboard.robot' on line 281: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-30T14:11:20.919068" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\Dashboard.robot' on line 292: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-30T14:11:20.920104" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\Dashboard.robot' on line 305: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-30T14:11:20.921124" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\Dashboard.robot' on line 435: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-30T14:11:20.987964" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\DatabaseConnector.robot' on line 355: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-30T14:11:20.989969" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\DatabaseConnector.robot' on line 389: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-30T14:11:21.298682" level="ERROR">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\SetEnvironmentVariales.robot' on line 14: Importing library 'C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py' failed: ModuleNotFoundError: No module named 'acintegration'
Traceback (most recent call last):
  File "C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py", line 1, in &lt;module&gt;
    from acintegration.QMetryIntegration import QMetryIntegration
PYTHONPATH:
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\robot.exe
  C:\Alternative
  C:\Alternative\Alternative_Physical_Channels_QA
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages</msg>
<msg time="2025-06-30T14:11:21.317026" level="WARN">Imported library 'C:\Alternative\Alternative_Physical_Channels_QA\future_fit_architecture_portal_BinTables\keywords\controllers\resources\RestRequestsUnMarshal.py' contains no keywords.</msg>
<msg time="2025-06-30T14:11:22.186405" level="WARN">There was error during termination of process</msg>
<msg time="2025-06-30T14:11:22.194028" level="WARN">exexcutable_path is being deprecated. Please use service to configure the driver's executable_path as per documentation.</msg>
<msg time="2025-06-30T14:11:41.768296" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-30T14:11:51.807100" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-30T14:11:56.834307" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
</errors>
</robot>
