*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                                        VMS HEALTHCHECK    DASHBOARD
Suite Setup                                         Set up environment variables
Test Teardown                                       The user logs out of VMS

Documentation                                       Successful login redirects user to the Dashboard 

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem
Library                                             DatabaseLibrary


#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../common_utilities/Login.robot 
Resource                                            ../../../common_utilities/Logout.robot    
Resource                                            ../../keywords/VMSPage/Dashboard.robot
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot

*** Keywords ***
Dashboard Validation
    [Arguments]  ${DOCUMENTATION}  ${TEST_ENVIRONMENT}    
    Set Test Documentation  ${DOCUMENTATION} 

    Given The user logs into the VMS Web Application     ${TEST_ENVIRONMENT}  

    Then The user lands on the dashboard page


| *** Test Case ***                                                                |                *DOCUMENTATION*                      |     *TEST_ENVIRONMENT*   |        
| Successful Login Redirects to Dashboard | Dashboard Validation    | User lans on VMS Dashboard after successful login   |      VMS_UAT             |
