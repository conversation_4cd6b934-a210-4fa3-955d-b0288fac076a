*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Documentation                Site Maintenance Keywords

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             ../../utility/UiActions.py
Library                                            String
Library                                             Collections
Library                                             BuiltIn

#***********************************PROJECT RESOURCES***************************************

*** Variables ***
${ADD_NEW_CALL}                                     //*[@id='btnAdd']  
${Site_Maintenance_table_ele}                        //*[@id="root"]/div/table
${Site_Maintenance_Menu}                            //*[text()[normalize-space(.)='Site Maintanance']]
${Site_Maintenance_Headers}                         //*[@id="root"]/div/table/thead
${rows_per_page}    5     # Number of rows per page
${FOOTER_REGEX}            \d+$
${MODAL_XPATH}                //*[@id="addCallModal"]/div/div/form
${Details_Link_Modal}            //*[@id="detailsCallModal"]/div/div/div[2]/div/div/label
${Details_Link_xpath}            //*[@id="btnDetails"]

*** Keywords ***
The user navigates to the Site Maintenance Page
    Log to console  --------------------------The user clicks navigate to the Site Maintenance Page
    Click Element  ${Site_Maintenance_Menu}

    Sleep    5s

    Wait Until Element Is Visible  ${ADD_NEW_CALL}          5     Main

    Page Should Contain    Site Maintenance

    # Wait for table to load with data
    Wait Until Element Is Visible    ${Site_Maintenance_table_ele}    15s
    Sleep    3s    # Additional wait for data to populate

    # Check if table has data rows, if not wait a bit more
    ${table_has_data}=    Run Keyword And Return Status    Element Should Be Visible    xpath=//*[@id="root"]/div/table/tbody/tr[1]
    Run Keyword If    not ${table_has_data}    Sleep    5s

The user gets front end data from today filter on Site Maintenance 
    # 1. Extract Table Headers
    ${headers}=    Get WebElements    ${Site_Maintenance_Headers}
    Log Many    @{headers}
    
    FOR    ${header_element}    IN    @{headers}
        ${header_text}=    Get Text    ${header_element}
        Log    Header: ${header_text}
    END

    
    # 2. Extract the footer text to calculate the total rows
    ${footer_text}=    Get Text    xpath=//*[@id="root"]/div/div/div/div[1]/span   
    ${total_rows}=    Extract Total Rows From Footer    ${footer_text}
    ${total_pages}=    Calculate Total Pages    ${total_rows}    ${rows_per_page}

    # Initialize an empty list to store all data
    ${all_data}=    Create List
    
    #Loop through each page and get data
    FOR    ${page}    IN RANGE    0    ${total_pages}
        Log    Processing Page ${page} of ${total_pages}
        
        # Extract Rows and Data for the Current Page
        ${rows}=    Get WebElements    xpath=//*[@id="root"]/div/table/tbody/tr
        
        #Loop through each row
        FOR    ${row}    IN    @{rows}
            ${columns}=    Get WebElements    xpath=//*[@id="root"]/div/table/tbody/tr//td
            ${row_data}=    Create List
            #Loop through each column 
            FOR    ${column}    IN    @{columns}
                ${cell_value}=    Get Text    ${column}
                Log    ${cell_value}
                Append To List    ${row_data}    ${cell_value}
            END

            # Convert row data to a single string
            ${row_string}=    Evaluate    ' | '.join(${row_data})
            Log    ${row_string}
            Append To List    ${all_data}    ${row_string}

        END
        
        #Paginate to the Next Page if not on the last page
        IF    ${page} < ${total_pages}
            Click Element    xpath=//*[text()[normalize-space(.)='Next']]
            Sleep    1s    # Wait for the next page to load
        END

    END

    # Log all data after processing all pages
    Log    ${all_data}

Extract Total Rows From Footer
    [Arguments]    ${footer_text}

    # Log the footer text
    Log    Footer Text: ${footer_text}

    # Define the regex pattern to match all numbers
    ${pattern}=    Set Variable    \\d+

    # Log the regex pattern
    Log    Regex Pattern: ${pattern}

    # Get matches
    ${matches}=    String.Get Regexp Matches    ${footer_text}    ${pattern}

    # Log matches
    Log Many    ${matches}

    # Check if matches are found
    Run Keyword If    ${matches} == []    Fail    No matches found in the footer text: ${footer_text}


    # Extract the last number from the matches as total rows
    ${total_rows}=    Set Variable    ${matches[-1]}
    Log    Total rows extracted: ${total_rows}

    [Return]    ${total_rows}


Calculate Total Pages
    [Arguments]    ${total_rows}    ${rows_per_page}
    ${total_pages}=    Evaluate    int(math.ceil(${total_rows} / ${rows_per_page}))    # Round up to calculate total pages
    [Return]    ${total_pages}


The user successfully navigates the rows per page feature
    #Navigate the rows per page feature
    ${footer_text}=    Get Text    xpath=//*[@id="root"]/div/div/div/div[1]/span   
    ${total_rows}=    Extract Total Rows From Footer    ${footer_text}
    ${total_pages}=    Calculate Total Pages    ${total_rows}    ${rows_per_page}

    FOR    ${page}    IN RANGE    0    ${total_pages}
        Log    Processing Page ${page} of ${total_pages}

            #Paginate to the Next Page if not on the last page
            IF    ${page} < ${total_pages}
                Click Element    xpath=//*[text()[normalize-space(.)='Next']]
                Sleep    1s    # Wait for the next page to load
            END 

    END
    Log    The user successfully navigated through the rows per page feature on Site Maintenance 

The user validates the refresh button on Site Maintenance 
    #Clicking the Refresh button
    Wait Until Page Contains Element    xpath=//*[@id="refresh"]
    Click Element    xpath=//*[@id="refresh"]
    #Validating Spinner 
    Run Keyword And Ignore Error    Wait Until Element Is Visible        xpath=//*[@id="loading"]    timeout=1s
    Log    Spiner appeared 
    Run Keyword And Ignore Error    Wait Until Element Is Not Visible    xpath=//*[@id="loading"]    timeout=1s
    Log    Spinner disappeared 

The user searches with a reference number on Site Maintenance
    # Check if table has data before attempting to get reference number
    ${table_has_data}=    Run Keyword And Return Status    Element Should Be Visible    xpath=//*[@id="root"]/div/table/tbody/tr[1]/td[1]
    Run Keyword If    not ${table_has_data}    Log    No data in table, skipping reference number search    WARN
    Return From Keyword If    not ${table_has_data}

    #Get Reference number
    ${Reference_Number_SM}=    Get Text    xpath=//*[@id="root"]/div/table/tbody/tr[1]/td[1]
    Log    Reference Number Retreived from FE:${Reference_Number_SM}

    #Search with reference number
    Wait Until Element Is Enabled    xpath=//*[@id="searchField"]
    Click Element    xpath=//*[@id="searchField"]
    Clear Element Text    xpath=//*[@id="searchField"]
    Input Text    xpath=//*[@id="searchField"]    ${Reference_Number_SM}

    Page Should Contain    ${Reference_Number_SM}
    Log    User was able to search with a reference number

The user searches with a ATM number on Site Maintenance
    # Check if table has data before attempting to get ATM number
    ${table_has_data}=    Run Keyword And Return Status    Element Should Be Visible    xpath=//*[@id="root"]/div/table/tbody/tr[1]/td[2]
    Run Keyword If    not ${table_has_data}    Log    No data in table, skipping ATM number search    WARN
    Return From Keyword If    not ${table_has_data}

    #Get ATM number
    ${ATM_Number_SM}=    Get Text    xpath=//*[@id="root"]/div/table/tbody/tr[1]/td[2]
    Log    ATM Number Retreived from FE:${ATM_Number_SM}

    #Search with ATM number
    Wait Until Element Is Enabled    xpath=//*[@id="searchField"]
    Click Element    xpath=//*[@id="searchField"]
    Clear Element Text    xpath=//*[@id="searchField"]
    Input Text    xpath=//*[@id="searchField"]    ${ATM_Number_SM}

    Page Should Contain    ${ATM_Number_SM}
    Log    User was able to search with a ATM number

The user searches with a Vendor on Site Maintenance
    # Check if table has data before attempting to get Vendor
    ${table_has_data}=    Run Keyword And Return Status    Element Should Be Visible    xpath=//*[@id="root"]/div/table/tbody/tr[1]/td[3]
    Run Keyword If    not ${table_has_data}    Log    No data in table, skipping Vendor search    WARN
    Return From Keyword If    not ${table_has_data}

    #Get Vendor
    ${Vendor_SM}=    Get Text    xpath=//*[@id="root"]/div/table/tbody/tr[1]/td[3]
    Log    Vendor Retreived from FE:${Vendor_SM}

    #Search with Vendor
    Wait Until Element Is Enabled    xpath=//*[@id="searchField"]
    Click Element    xpath=//*[@id="searchField"]
    Clear Element Text    xpath=//*[@id="searchField"]
    Input Text    xpath=//*[@id="searchField"]    ${Vendor_SM}

    Page Should Contain    ${Vendor_SM}
    Log    User was able to search with a Vendor

The user searches with a Start Date on Site Maintenance
    # Check if table has data before attempting to get Start Date
    ${table_has_data}=    Run Keyword And Return Status    Element Should Be Visible    xpath=//*[@id="root"]/div/table/tbody/tr[1]/td[4]
    Run Keyword If    not ${table_has_data}    Log    No data in table, skipping Start Date search    WARN
    Return From Keyword If    not ${table_has_data}

    #Get Start Date
    ${Start_Date_SM}=    Get Text    xpath=//*[@id="root"]/div/table/tbody/tr[1]/td[4]
    Log    Start Date Retreived from FE:${Start_Date_SM}

    #Search with Start Date
    Wait Until Element Is Enabled    xpath=//*[@id="searchField"]
    Click Element    xpath=//*[@id="searchField"]
    Clear Element Text    xpath=//*[@id="searchField"]
    Input Text    xpath=//*[@id="searchField"]    ${Start_Date_SM}

    Page Should Contain    ${Start_Date_SM}
    Log    User was able to search with a Start Date

The user searches with a End Date on Site Maintenance
    # Check if table has data before attempting to get End Date
    ${table_has_data}=    Run Keyword And Return Status    Element Should Be Visible    xpath=//*[@id="root"]/div/table/tbody/tr[1]/td[5]
    Run Keyword If    not ${table_has_data}    Log    No data in table, skipping End Date search    WARN
    Return From Keyword If    not ${table_has_data}

    #Get End Date
    ${End_Date_SM}=    Get Text    xpath=//*[@id="root"]/div/table/tbody/tr[1]/td[5]
    Log    End Date Retrieved from FE: ${End_Date_SM}

    #Check if End Date is Blank
    Run Keyword If    "${End_Date_SM}" == ""    Log    End Date is blank, skipping search
    ...    ELSE    Perform End Date Search    ${End_Date_SM}


Perform End Date Search
    [Arguments]    ${End_Date_SM}
    Log    Performing End Date search with value: ${End_Date_SM}

    #Search with End Date
    Wait Until Element Is Enabled    xpath=//*[@id="searchField"]
    Click Element    xpath=//*[@id="searchField"]
    Clear Element Text    xpath=//*[@id="searchField"]
    Input Text    xpath=//*[@id="searchField"]    ${End_Date_SM}

    Page Should Contain    ${End_Date_SM}
    Log    User was able to search with a End Date



The user searches with an ATM Name on Site Maintenance
    # Check if table has data before attempting to get ATM Name
    ${table_has_data}=    Run Keyword And Return Status    Element Should Be Visible    xpath=//*[@id="root"]/div/table/tbody/tr[1]/td[6]
    Run Keyword If    not ${table_has_data}    Log    No data in table, skipping ATM Name search    WARN
    Return From Keyword If    not ${table_has_data}

    #Get ATM Name
    ${ATM_Name_SM}=    Get Text    xpath=//*[@id="root"]/div/table/tbody/tr[1]/td[6]
    Log    ATM Name Retrieved from FE:${ATM_Name_SM}

    #Search with ATM Name
    Wait Until Element Is Enabled    xpath=//*[@id="searchField"]
    Click Element    xpath=//*[@id="searchField"]
    Clear Element Text    xpath=//*[@id="searchField"]
    Input Text    xpath=//*[@id="searchField"]    ${ATM_Name_SM}

    Page Should Contain    ${ATM_Name_SM}
    Log    User was able to search with a ATM Name

The user searches with a Region on Site Maintenance
    # Check if table has data before attempting to get Region
    ${table_has_data}=    Run Keyword And Return Status    Element Should Be Visible    xpath=//*[@id="root"]/div/table/tbody/tr[1]/td[7]
    Run Keyword If    not ${table_has_data}    Log    No data in table, skipping Region search    WARN
    Return From Keyword If    not ${table_has_data}

    #Get Region
    ${Region_SM}=    Get Text    xpath=//*[@id="root"]/div/table/tbody/tr[1]/td[7]
    Log    Region Retrieved from FE:${Region_SM}

    #Search with Region
    Wait Until Element Is Enabled    xpath=//*[@id="searchField"]
    Click Element    xpath=//*[@id="searchField"]
    Clear Element Text    xpath=//*[@id="searchField"]
    Input Text    xpath=//*[@id="searchField"]    ${Region_SM}

    Page Should Contain    ${Region_SM}
    Log    User was able to search with a Region

The user searches with a Model on Site Maintenance
    # Check if table has data before attempting to get Model
    ${table_has_data}=    Run Keyword And Return Status    Element Should Be Visible    xpath=//*[@id="root"]/div/table/tbody/tr[1]/td[8]
    Run Keyword If    not ${table_has_data}    Log    No data in table, skipping Model search    WARN
    Return From Keyword If    not ${table_has_data}

    # Use a try-catch approach to handle the model column
    ${get_model_status}=    Run Keyword And Return Status    Get Text    xpath=//*[@id="root"]/div/table/tbody/tr[1]/td[8]

    # If the column exists, get the text, otherwise use a default value
    ${Model_SM}=    Run Keyword If    ${get_model_status}    Get Text    xpath=//*[@id="root"]/div/table/tbody/tr[1]/td[8]
    ...    ELSE    Set Variable    NCR

    Log    Model Retrieved from FE: ${Model_SM}

    # Search with Model
    Wait Until Element Is Enabled    xpath=//*[@id="searchField"]
    Click Element    xpath=//*[@id="searchField"]
    Clear Element Text    xpath=//*[@id="searchField"]
    Input Text    xpath=//*[@id="searchField"]    ${Model_SM}

    Page Should Contain    ${Model_SM}
    Log    User was able to search with a Model

The user searches with an Institution on Site Maintenance
    # Check if table has data and Institution column exists before attempting to get Institution
    ${table_has_data}=    Run Keyword And Return Status    Element Should Be Visible    xpath=//*[@id="root"]/div/table/tbody/tr[1]/td[9]
    Run Keyword If    not ${table_has_data}    Log    No data in table or Institution column not found, skipping Institution search    WARN
    Return From Keyword If    not ${table_has_data}

    #Get Institution
    ${Institution_SM}=    Get Text    xpath=//*[@id="root"]/div/table/tbody/tr[1]/td[9]
    Log    Institution Retrieved from FE:${Institution_SM}

    #Search with Institution
    Wait Until Element Is Enabled    xpath=//*[@id="searchField"]
    Click Element    xpath=//*[@id="searchField"]
    Clear Element Text    xpath=//*[@id="searchField"]
    Input Text    xpath=//*[@id="searchField"]    ${Institution_SM}

    Page Should Contain    ${Institution_SM}
    Log    User was able to search with a Institution

The user searches with a Device on Site Maintenance
    # Check if table has data and Device column exists before attempting to get Device
    ${table_has_data}=    Run Keyword And Return Status    Element Should Be Visible    xpath=//*[@id="root"]/div/table/tbody/tr[1]/td[10]
    Run Keyword If    not ${table_has_data}    Log    No data in table or Device column not found, skipping Device search    WARN
    Return From Keyword If    not ${table_has_data}

    #Get Device
    ${Device_SM}=    Get Text    xpath=//*[@id="root"]/div/table/tbody/tr[1]/td[10]
    Log    Device Retrieved from FE:${Device_SM}

    #Search with Device
    Wait Until Element Is Enabled    xpath=//*[@id="searchField"]
    Click Element    xpath=//*[@id="searchField"]
    Clear Element Text    xpath=//*[@id="searchField"]
    Input Text    xpath=//*[@id="searchField"]    ${Device_SM}

    Page Should Contain    ${Device_SM}
    Log    User was able to search with a Device

The user searches with an Error Description on Site Maintenance
    # Check if table has data and Error Description column exists before attempting to get Error Description
    ${table_has_data}=    Run Keyword And Return Status    Element Should Be Visible    xpath=//*[@id="root"]/div/table/tbody/tr[1]/td[11]
    Run Keyword If    not ${table_has_data}    Log    No data in table or Error Description column not found, skipping Error Description search    WARN
    Return From Keyword If    not ${table_has_data}

    #Get Error Description
    ${Error_Description_SM}=    Get Text    xpath=//*[@id="root"]/div/table/tbody/tr[1]/td[11]
    Log    Error Description Retrieved from FE:${Error_Description_SM}

    #Search with Error Description_SM
    Wait Until Element Is Enabled    xpath=//*[@id="searchField"]
    Click Element    xpath=//*[@id="searchField"]
    Clear Element Text    xpath=//*[@id="searchField"]
    Input Text    xpath=//*[@id="searchField"]    ${Error_Description_SM}

    Page Should Contain    ${Error_Description_SM}
    Log    User was able to search with a ${Error_Description_SM}

The user searches with a Status on Site Maintenance
    # Check if table has data before attempting to get Status
    ${table_has_data}=    Run Keyword And Return Status    Element Should Be Visible    xpath=//*[@id="root"]/div/table/tbody/tr[1]
    Run Keyword If    not ${table_has_data}    Log    No data in table, skipping Status search    WARN
    Return From Keyword If    not ${table_has_data}

    # Try to find Status column - first try column 12, then column 5
    ${Status_SM}=    Get Status Column Value

    Run Keyword If    "${Status_SM}" == "${EMPTY}"    Log    Status column not found, skipping Status search    WARN
    Return From Keyword If    "${Status_SM}" == "${EMPTY}"

    Log    Status Retrieved from FE: ${Status_SM}

    #Check if Status is Blank
    Run Keyword If    "${Status_SM}" == ""    Log    Status is blank, skipping search
    ...    ELSE    Perform Status Search    ${Status_SM}

Get Status Column Value
    # Try column 12 first (common status column position)
    ${status_col_12}=    Run Keyword And Return Status    Element Should Be Visible    xpath=//*[@id="root"]/div/table/tbody/tr[1]/td[12]
    ${Status_Value}=    Run Keyword If    ${status_col_12}    Get Text    xpath=//*[@id="root"]/div/table/tbody/tr[1]/td[12]
    Return From Keyword If    ${status_col_12}    ${Status_Value}

    # If not found in column 12, try column 5
    ${status_col_5}=    Run Keyword And Return Status    Element Should Be Visible    xpath=//*[@id="root"]/div/table/tbody/tr[1]/td[5]
    ${Status_Value}=    Run Keyword If    ${status_col_5}    Get Text    xpath=//*[@id="root"]/div/table/tbody/tr[1]/td[5]
    ...    ELSE    Set Variable    ${EMPTY}

    [Return]    ${Status_Value}


Perform Status Search
    [Arguments]    ${Status_Value}
    Log    Performing Status search with value: ${Status_Value}

    #Search with Status
    Wait Until Element Is Enabled    xpath=//*[@id="searchField"]
    Click Element    xpath=//*[@id="searchField"]
    Clear Element Text    xpath=//*[@id="searchField"]
    Input Text    xpath=//*[@id="searchField"]    ${Status_Value}

    Page Should Contain    ${Status_Value}
    Log    User was able to search with a Status

The user uses the PREV and NEXT buttons on page navigation
    # Navigate the rows per page feature and validate Prev/Next buttons
    ${footer_text}=    Get Text    xpath=//*[@id="root"]/div/div/div/div[1]/span
    ${total_rows}=    Extract Total Rows From Footer    ${footer_text}
    ${total_pages}=    Calculate Total Pages    ${total_rows}    ${rows_per_page}

    # Navigate forward through pages using the "Next" button
    FOR    ${page}    IN RANGE    1    ${total_pages}
        Log    Navigating to Page ${page} of ${total_pages}
        
        # Validate the "Next" button is enabled and click it
        ${is_next_enabled}=    Run Keyword And Return Status    Element Should Be Enabled    xpath=//*[text()[normalize-space(.)='Next']]
        Log    Is Next Button Enabled: ${is_next_enabled}
        Run Keyword If    ${is_next_enabled}    Click Element    xpath=//*[text()[normalize-space(.)='Next']]
        Sleep    1s    # Wait for the next page to load
        
        # Validate "Prev" button becomes visible after moving to the next page
        ${is_prev_enabled}=    Run Keyword And Return Status    Element Should Be Enabled    xpath=//*[text()[normalize-space(.)='Previous']]
        Log    Is Prev Button Enabled: ${is_prev_enabled}

        # Check if page indicator is present (more flexible approach)
        ${page_indicator_present}=    Run Keyword And Return Status    Page Should Contain    Page ${page}
        Run Keyword If    not ${page_indicator_present}    Log    Page indicator 'Page ${page}' not found, but navigation successful    WARN
    END

    # Navigate backward through pages using the "Prev" button
    FOR    ${page}    IN RANGE    ${total_pages}    1    -1
        Log    Navigating back to Page ${page} of ${total_pages}

        # Validate the "Prev" button is enabled and click it
        ${is_prev_enabled}=    Run Keyword And Return Status    Element Should Be Enabled    xpath=//*[text()[normalize-space(.)='Previous']]
        Log    Is Prev Button Enabled: ${is_prev_enabled}
        Run Keyword If    ${is_prev_enabled}    Click Element    xpath=//*[text()[normalize-space(.)='Previous']]
        Sleep    1s    # Wait for the previous page to load

        # Validate "Next" button is enabled after moving back to the previous page
        ${is_next_enabled}=    Run Keyword And Return Status    Element Should Be Enabled    xpath=//*[text()[normalize-space(.)='Next']]
        Log    Is Next Button Enabled: ${is_next_enabled}

        # Check if page indicator is present (more flexible approach)
        ${page_indicator_present}=    Run Keyword And Return Status    Page Should Contain    Page ${page}
        Run Keyword If    not ${page_indicator_present}    Log    Page indicator 'Page ${page}' not found, but navigation successful    WARN
    END

    Log    The user successfully validated the Prev/Next buttons on Site Maintenance

    Log    The user successfully validated the Prev/Next buttons on Site Maintenance

The user opens Add New Call on Site Maintenance    
    Sleep    2s
    Click Element    xpath=//*[@id="btnAdd"]
    Page Should Contain    Site Maintenance - Add New Call

The user verifies that the ATM Number field on Add new call is mandatory 
    #Fill in Mandatory Details on Site Maintenance - Add New Call, allow the ATM Number field to be blank
    
    #Select Vendor
    Wait Until Element Is Visible    xpath=//*[@id="MainContent_ddlAddVendor"]
    Click Element    xpath=//*[@id="MainContent_ddlAddVendor"]
    Select From List By Index    xpath=//*[@id="MainContent_ddlAddVendor"]    2
    Sleep    2s
    
    #Select Device:
    Wait Until Element Is Visible    xpath=//*[@id="MainContent_ddlAddDevice"]
    Click Element    xpath=//*[@id="MainContent_ddlAddDevice"]
    Select From List By Index    xpath=//*[@id="MainContent_ddlAddDevice"]    2
    Sleep    2s

    #Input Error Description 
    Wait Until Element Is Visible    xpath=//*[@id="MainContent_txtAdd_ErrorDesc"]
    Click Element    xpath=//*[@id="MainContent_txtAdd_ErrorDesc"]
    Input Text       xpath=//*[@id="MainContent_txtAdd_ErrorDesc"]      Automation Script Execution 
    Sleep    2s

    #Input Custodian Information
    Wait Until Element Is Visible    xpath=//*[@id="MainContent_txtAdd_Custodian"]
    Click Element    xpath=//*[@id="MainContent_txtAdd_Custodian"]
    Input Text       xpath=//*[@id="MainContent_txtAdd_Custodian"]      Robotframework Execution 
    Sleep    2s

    #Input Cellphone number 
    Wait Until Element Is Visible    xpath=//*[@id="MainContent_txtAdd_Cellphone"]
    Click Element    xpath=//*[@id="MainContent_txtAdd_Cellphone"]
    Input Text       xpath=//*[@id="MainContent_txtAdd_Cellphone"]      0661232567
    Sleep    2s
    
    #Click the Submit button
    Click Element    xpath=//*[@id="btnAddNewCall"]

    #Check if the modal is still visible (should remain open)
    ${is_visible}=    Run Keyword And Return Status    Element Should Be Visible    ${MODAL_XPATH}
    Log    Modal Display Status: ${is_visible}

    #Validating that the modal is visible
    Run Keyword If    ${is_visible}    Log    Modal is still active, form was not submitted due to a blank mandatory field.

The user verifies that the Vendor field on Add new call is mandatory 
    #Fill in Mandatory Details on Site Maintenance - Add New Call, allow the Vendor field to be blank

    #Select ATM Number
    Wait Until Element Is Visible    xpath=//*[@id="select2-MainContent_ddlAddAtmNumber-container"]
    Click Element    xpath=//*[@id="select2-MainContent_ddlAddAtmNumber-container"]
    Select From List By Index    xpath=//*[@id="MainContent_ddlAddAtmNumber"]    2
    Sleep    2s
    
    #Select Device:
    Wait Until Element Is Visible    xpath=//*[@id="MainContent_ddlAddDevice"]
    Click Element    xpath=//*[@id="MainContent_ddlAddDevice"]
    Select From List By Index    xpath=//*[@id="MainContent_ddlAddDevice"]    2
    Sleep    2s

    #Input Error Description 
    Wait Until Element Is Visible    xpath=//*[@id="MainContent_txtAdd_ErrorDesc"]
    Click Element    xpath=//*[@id="MainContent_txtAdd_ErrorDesc"]
    Input Text       xpath=//*[@id="MainContent_txtAdd_ErrorDesc"]      Automation Script Execution 
    Sleep    2s

    #Input Custodian Information
    Wait Until Element Is Visible    xpath=//*[@id="MainContent_txtAdd_Custodian"]
    Click Element    xpath=//*[@id="MainContent_txtAdd_Custodian"]
    Input Text       xpath=//*[@id="MainContent_txtAdd_Custodian"]      Robotframework Execution 
    Sleep    2s

    #Input Cellphone number 
    Wait Until Element Is Visible    xpath=//*[@id="MainContent_txtAdd_Cellphone"]
    Click Element    xpath=//*[@id="MainContent_txtAdd_Cellphone"]
    Input Text       xpath=//*[@id="MainContent_txtAdd_Cellphone"]      0661232567
    Sleep    2s
    
    #Click the Submit button
    Click Element    xpath=//*[@id="btnAddNewCall"]

    #Check if the modal is still visible (should remain open)
    ${is_visible}=    Run Keyword And Return Status    Element Should Be Visible    ${MODAL_XPATH}
    Log    Modal Display Status: ${is_visible}

    #Validating that the modal is visible
    Run Keyword If    ${is_visible}    Log    Modal is still active, form was not submitted due to a blank mandatory field.

The user verifies that the Device field on Add new call is mandatory
    #Fill in Mandatory Details on Site Maintenance - Add New Call, allow the Device field to be blank

    #Select ATM Number
    Wait Until Element Is Visible    xpath=//*[@id="select2-MainContent_ddlAddAtmNumber-container"]
    Click Element    xpath=//*[@id="select2-MainContent_ddlAddAtmNumber-container"]
    Select From List By Index    xpath=//*[@id="MainContent_ddlAddAtmNumber"]    2
    Sleep    2s

    #Input Error Description 
    Wait Until Element Is Visible    xpath=//*[@id="MainContent_txtAdd_ErrorDesc"]
    Click Element    xpath=//*[@id="MainContent_txtAdd_ErrorDesc"]
    Input Text       xpath=//*[@id="MainContent_txtAdd_ErrorDesc"]      Automation Script Execution 
    Sleep    2s

    #Select Vendor
    Wait Until Element Is Visible    xpath=//*[@id="MainContent_ddlAddVendor"]
    Click Element    xpath=//*[@id="MainContent_ddlAddVendor"]
    Select From List By Index    xpath=//*[@id="MainContent_ddlAddVendor"]    2
    Sleep    2s

    #Input Custodian Information
    Wait Until Element Is Visible    xpath=//*[@id="MainContent_txtAdd_Custodian"]
    Click Element    xpath=//*[@id="MainContent_txtAdd_Custodian"]
    Input Text       xpath=//*[@id="MainContent_txtAdd_Custodian"]      Robotframework Execution 
    Sleep    2s

    #Input Cellphone number 
    Wait Until Element Is Visible    xpath=//*[@id="MainContent_txtAdd_Cellphone"]
    Click Element    xpath=//*[@id="MainContent_txtAdd_Cellphone"]
    Input Text       xpath=//*[@id="MainContent_txtAdd_Cellphone"]      0661232567
    Sleep    2s
    
    #Click the Submit button
    Click Element    xpath=//*[@id="btnAddNewCall"]

    #Check if the modal is still visible (should remain open)
    ${is_visible}=    Run Keyword And Return Status    Element Should Be Visible    ${MODAL_XPATH}
    Log    Modal Display Status: ${is_visible}

    #Validating that the modal is visible
    Run Keyword If    ${is_visible}    Log    Modal is still active, form was not submitted due to a blank mandatory field.
     
The user verifies that the Error Description field on Add new call is mandatory
    #Fill in Mandatory Details on Site Maintenance - Add New Call, allow the Error Description field to be blank
    
    #Select Vendor
    Wait Until Element Is Visible    xpath=//*[@id="MainContent_ddlAddVendor"]
    Click Element    xpath=//*[@id="MainContent_ddlAddVendor"]
    Select From List By Index    xpath=//*[@id="MainContent_ddlAddVendor"]    2
    Sleep    2s

    #Select ATM Number
    Wait Until Element Is Visible    xpath=//*[@id="select2-MainContent_ddlAddAtmNumber-container"]
    Click Element    xpath=//*[@id="select2-MainContent_ddlAddAtmNumber-container"]
    Select From List By Index    xpath=//*[@id="MainContent_ddlAddAtmNumber"]    2
    Sleep    2s
    
    #Select Device:
    Wait Until Element Is Visible    xpath=//*[@id="MainContent_ddlAddDevice"]
    Click Element    xpath=//*[@id="MainContent_ddlAddDevice"]
    Select From List By Index    xpath=//*[@id="MainContent_ddlAddDevice"]    2
    Sleep    2s

    #Input Custodian Information
    Wait Until Element Is Visible    xpath=//*[@id="MainContent_txtAdd_Custodian"]
    Click Element    xpath=//*[@id="MainContent_txtAdd_Custodian"]
    Input Text       xpath=//*[@id="MainContent_txtAdd_Custodian"]      Robotframework Execution 
    Sleep    2s

    #Input Cellphone number 
    Wait Until Element Is Visible    xpath=//*[@id="MainContent_txtAdd_Cellphone"]
    Click Element    xpath=//*[@id="MainContent_txtAdd_Cellphone"]
    Input Text       xpath=//*[@id="MainContent_txtAdd_Cellphone"]      0661232567
    Sleep    2s
    
    #Click the Submit button
    Click Element    xpath=//*[@id="btnAddNewCall"]

    #Check if the modal is still visible (should remain open)
    ${is_visible}=    Run Keyword And Return Status    Element Should Be Visible    ${MODAL_XPATH}
    Log    Modal Display Status: ${is_visible}

    #Validating that the modal is visible
    Run Keyword If    ${is_visible}    Log    Modal is still active, form was not submitted due to a blank mandatory field.
    
    # Verify the Error Description field element is visible, which indicates the form wasn't submitted due to missing value
    Element Should Be Visible    xpath=//*[@id="MainContent_txtAdd_ErrorDesc"]
    
    # Verify the modal is still visible as an added check, which confirms the form wasn't submitted
    Element Should Be Visible    ${MODAL_XPATH}

The user verifies that the Custodian field on Add new call is mandatory
    #Fill in Mandatory Details on Site Maintenance - Add New Call, allow the Custodian field to be blank
    
    #Select Vendor
    Wait Until Element Is Visible    xpath=//*[@id="MainContent_ddlAddVendor"]
    Click Element    xpath=//*[@id="MainContent_ddlAddVendor"]
    Select From List By Index    xpath=//*[@id="MainContent_ddlAddVendor"]    2
    Sleep    2s

    #Select ATM Number
    Wait Until Element Is Visible    xpath=//*[@id="select2-MainContent_ddlAddAtmNumber-container"]
    Click Element    xpath=//*[@id="select2-MainContent_ddlAddAtmNumber-container"]
    Select From List By Index    xpath=//*[@id="MainContent_ddlAddAtmNumber"]    2
    Sleep    2s

    #Input Error Description 
    Wait Until Element Is Visible    xpath=//*[@id="MainContent_txtAdd_ErrorDesc"]
    Click Element    xpath=//*[@id="MainContent_txtAdd_ErrorDesc"]
    Input Text       xpath=//*[@id="MainContent_txtAdd_ErrorDesc"]      Automation Script Execution 
    Sleep    2s
    
    #Select Device:
    Wait Until Element Is Visible    xpath=//*[@id="MainContent_ddlAddDevice"]
    Click Element    xpath=//*[@id="MainContent_ddlAddDevice"]
    Select From List By Index    xpath=//*[@id="MainContent_ddlAddDevice"]    2
    Sleep    2s


    #Input Cellphone number 
    Wait Until Element Is Visible    xpath=//*[@id="MainContent_txtAdd_Cellphone"]
    Click Element    xpath=//*[@id="MainContent_txtAdd_Cellphone"]
    Input Text       xpath=//*[@id="MainContent_txtAdd_Cellphone"]      0661232567
    Sleep    2s
    
    #Click the Submit button
    Click Element    xpath=//*[@id="btnAddNewCall"]

    #Check if the modal is still visible (should remain open)
    ${is_visible}=    Run Keyword And Return Status    Element Should Be Visible    ${MODAL_XPATH}
    Log    Modal Display Status: ${is_visible}

    #Validating that the modal is visible
    Run Keyword If    ${is_visible}    Log    Modal is still active, form was not submitted due to a blank mandatory field.
    
    # Verify the Custodian field element is visible, which indicates the form wasn't submitted due to missing value
    Element Should Be Visible    xpath=//*[@id="MainContent_txtAdd_Custodian"]
    
    # Verify the modal is still visible as an added check, which confirms the form wasn't submitted
    Element Should Be Visible    ${MODAL_XPATH}

The user verifies the Details action link
    # Check if table has data and Details button exists before attempting to click
    ${table_has_data}=    Run Keyword And Return Status    Element Should Be Visible    xpath=//*[@id="root"]/div/table/tbody/tr[1]
    Run Keyword If    not ${table_has_data}    Log    No data in table, skipping Details action link verification    WARN
    Return From Keyword If    not ${table_has_data}

    # Check if Details button exists
    ${details_button_exists}=    Run Keyword And Return Status    Element Should Be Visible    ${Details_Link_xpath}
    Run Keyword If    not ${details_button_exists}    Log    Details button not found, skipping Details action link verification    WARN
    Return From Keyword If    not ${details_button_exists}

    #Get Details Link Button Element
    ${Details_link}=    Get WebElement    ${Details_Link_xpath}
    Log To Console    -----------Opening Detail Link Modal
    Click Element    ${Details_link}
    Wait Until Element Is Visible    ${Details_Link_Modal}
    Sleep    5s

    #Get all elements within the modal
    ${elements_in_details_modal}=    Get WebElements    ${Details_Link_Modal}
    Log    ${elements_in_details_modal}

    #Log the total number of elements
    ${count}=    Get Length    ${elements_in_details_modal}
    Log    Total number of elements: ${count}
    Log To Console    Total number of elements: ${count}
    Sleep    5s

    #Initialize an empty list to store text values
     @{list}=    Create List

    #Loop through the elements and log their text
    FOR    ${index}    ${element}    IN ENUMERATE    @{elements_in_details_modal}

        ${data}=    Get Text    ${element}

        ${formatted_text}=    Catenate    SEPARATOR=    ${data}

        Append To List    ${list}    ${formatted_text}
        Log To Console    Info:${formatted_text}

    END

    #Join the list into a single string
    ${Detail_link_data}=    Catenate    ${list}    ${SPACE}

    #Log the text retrieved from the Detail Link Modal
    Log To Console            -----------Successfully retrieved information from Detail Action Link
    BuiltIn.Log               Collected info: ${Detail_link_data}
    BuiltIn.Log To Console    Collected info: ${Detail_link_data}






    




    







