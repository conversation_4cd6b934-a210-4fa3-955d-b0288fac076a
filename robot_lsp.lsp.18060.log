lsp: 2025-07-03 09:36:56 UTC pid: 18060 - MainThread - INFO - robotframework_ls.__main__
Arguments: ['--log-file=c:\\Alternative\\robot_lsp.log', '--verbose']

lsp: 2025-07-03 09:36:56 UTC pid: 18060 - MainThread - INFO - robotframework_ls.__main__
Python: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)] - lsp: 1.13.0 (<module 'robotframework_ls' from 'c:\\Users\\<USER>\\.vscode\\extensions\\robocorp.robotframework-lsp-1.13.0\\src\\robotframework_ls\\__init__.py'>) - platform: win32 - sys.prefix: C:\Users\<USER>\AppData\Local\Programs\Python\Python312 - sys.executable: C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe

lsp: 2025-07-03 09:36:56 UTC pid: 18060 - MainThread - INFO - robotframework_ls.__main__
CPUs: 12

lsp: 2025-07-03 09:36:56 UTC pid: 18060 - MainThread - INFO - robocorp_ls_core.python_ls
Starting RobotFrameworkLanguageServer IO language server. pid: 18060

lsp: 2025-07-03 09:36:56 UTC pid: 18060 - MainThread - INFO - robotframework_ls.robotframework_ls_impl
Using watch implementation: watchdog (customize with ROBOTFRAMEWORK_LS_WATCH_IMPL environment variable)

lsp: 2025-07-03 09:36:56 UTC pid: 18060 - MainThread - INFO - robocorp_ls_core.remote_fs_observer_impl
Initializing Remote FS Observer with the following args: ['C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python.exe', '-u', 'c:\\Users\\<USER>\\.vscode\\extensions\\robocorp.robotframework-lsp-1.13.0\\src\\robotframework_ls\\vendored\\robocorp_ls_core\\remote_fs_observer__main__.py', '--log-file=c:\\Alternative\\robot_lsp.log', '-v']

lsp: 2025-07-03 09:37:23 UTC pid: 18060 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 4, method: textDocument/foldingRange

lsp: 2025-07-03 09:37:23 UTC pid: 18060 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 6, method: textDocument/codeLens

lsp: 2025-07-03 09:37:23 UTC pid: 18060 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 5, method: textDocument/documentSymbol

lsp: 2025-07-03 09:37:23 UTC pid: 18060 - ThreadPoolExecutor-0_3 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000021D7DFAF560>

lsp: 2025-07-03 09:37:23 UTC pid: 18060 - ThreadPoolExecutor-0_2 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000021D7DFAF560>

lsp: 2025-07-03 09:37:23 UTC pid: 18060 - ThreadPoolExecutor-0_1 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000021D7DFAF560>

lsp: 2025-07-03 09:37:33 UTC pid: 18060 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 12, method: textDocument/codeAction

lsp: 2025-07-03 09:37:33 UTC pid: 18060 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 20, method: textDocument/hover

lsp: 2025-07-03 09:37:33 UTC pid: 18060 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 21, method: textDocument/hover

lsp: 2025-07-03 09:37:33 UTC pid: 18060 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 19, method: textDocument/codeLens

lsp: 2025-07-03 09:37:33 UTC pid: 18060 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 23, method: textDocument/hover

lsp: 2025-07-03 09:37:33 UTC pid: 18060 - ThreadPoolExecutor-0_1 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000021D7DFAF560>

lsp: 2025-07-03 09:37:33 UTC pid: 18060 - ThreadPoolExecutor-0_8 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000021D7DFAF560>

lsp: 2025-07-03 09:37:33 UTC pid: 18060 - ThreadPoolExecutor-0_10 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000021D7DFAF560>

lsp: 2025-07-03 09:37:33 UTC pid: 18060 - ThreadPoolExecutor-0_3 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000021D7DFAF560>

lsp: 2025-07-03 09:37:33 UTC pid: 18060 - ThreadPoolExecutor-0_4 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000021D7DFAF560>

lsp: 2025-07-03 09:37:33 UTC pid: 18060 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 16

lsp: 2025-07-03 09:37:33 UTC pid: 18060 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 22, method: textDocument/codeLens

lsp: 2025-07-03 09:37:33 UTC pid: 18060 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 18

lsp: 2025-07-03 09:37:33 UTC pid: 18060 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 17, method: textDocument/codeAction

lsp: 2025-07-03 09:37:34 UTC pid: 18060 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 27, method: textDocument/codeAction

lsp: 2025-07-03 09:37:34 UTC pid: 18060 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 36, method: textDocument/hover

lsp: 2025-07-03 09:37:34 UTC pid: 18060 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 35, method: textDocument/codeLens

lsp: 2025-07-03 09:37:34 UTC pid: 18060 - ThreadPoolExecutor-0_8 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000021D7DFAF560>

lsp: 2025-07-03 09:37:34 UTC pid: 18060 - ThreadPoolExecutor-0_11 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000021D7DFAF560>

